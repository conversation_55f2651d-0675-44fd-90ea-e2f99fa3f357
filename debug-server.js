require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

console.log('Starting debug server...');

try {
  console.log('1. Creating Express app...');
  const app = express();
  
  console.log('2. Setting up basic middleware...');
  app.use(helmet());
  app.use(cors());
  app.use(express.json());
  
  console.log('3. Setting up test route...');
  app.get('/health', (req, res) => {
    res.json({ 
      success: true, 
      message: 'Debug server is running',
      timestamp: new Date().toISOString()
    });
  });
  
  console.log('4. Testing logger import...');
  const logger = require('./utils/logger');
  console.log('✓ Logger imported');
  
  console.log('5. Testing auth middleware import...');
  const { protect } = require('./middleware/auth');
  console.log('✓ Auth middleware imported');
  
  console.log('6. Testing error handler import...');
  const { globalErrorHandler, notFound } = require('./middleware/errorHandler');
  console.log('✓ Error handler imported');
  
  console.log('7. Adding error handling middleware...');
  app.use(notFound);
  app.use(globalErrorHandler);
  
  console.log('8. Starting server...');
  const PORT = process.env.PORT || 5000;
  
  app.listen(PORT, () => {
    console.log(`✓ Debug server running on port ${PORT}`);
    console.log(`✓ Health check: http://localhost:${PORT}/health`);
  });
  
} catch (error) {
  console.error('❌ Error in debug server:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
