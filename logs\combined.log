{"ip":"::1","level":"warn","message":"404 Not Found:","method":"GET","timestamp":"2025-07-19 18:35:27:3527","url":"/favicon.ico","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"No default engine was specified and no extension was provided.","ip":"::1","level":"error","message":"Error in development:","method":"GET","stack":"Error: No default engine was specified and no extension was provided.\n    at new View (D:\\projects\\node_modules\\express\\lib\\view.js:61:11)\n    at Function.render (D:\\projects\\node_modules\\express\\lib\\application.js:587:12)\n    at ServerResponse.render (D:\\projects\\node_modules\\express\\lib\\response.js:1049:7)\n    at notFound (D:\\projects\\middleware\\errorHandler.js:236:19)\n    at Layer.handle [as handle_request] (D:\\projects\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\projects\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\projects\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\projects\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\projects\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\projects\\node_modules\\body-parser\\lib\\types\\json.js:113:7)","timestamp":"2025-07-19 18:35:27:3527","url":"/favicon.ico","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
