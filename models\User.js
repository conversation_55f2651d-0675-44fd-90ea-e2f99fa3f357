const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const userSchema = new mongoose.Schema({
  // Personal Information
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^[0-9]{10}$/, 'Please enter a valid 10-digit phone number']
  },
  
  // Government IDs
  aadhaarNumber: {
    type: String,
    required: [true, 'Aadhaar number is required'],
    unique: true,
    match: [/^[0-9]{12}$/, 'Please enter a valid 12-digit Aadhaar number'],
    select: false // Don't include in queries by default for security
  },
  voterIdNumber: {
    type: String,
    required: [true, 'Voter ID is required'],
    unique: true,
    uppercase: true,
    match: [/^[A-Z]{3}[0-9]{7}$/, 'Please enter a valid Voter ID']
  },
  
  // Address Information
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    district: { type: String, required: true },
    state: { type: String, required: true },
    pincode: { 
      type: String, 
      required: true,
      match: [/^[0-9]{6}$/, 'Please enter a valid 6-digit pincode']
    },
    village: { type: String }, // Optional for rural areas
    constituency: { type: String, required: true }
  },
  
  // Authentication
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    select: false
  },
  role: {
    type: String,
    enum: ['voter', 'admin', 'election_officer', 'observer', 'analyst'],
    default: 'voter'
  },
  
  // Verification Status
  isEmailVerified: { type: Boolean, default: false },
  isPhoneVerified: { type: Boolean, default: false },
  isDocumentVerified: { type: Boolean, default: false },
  isFaceVerified: { type: Boolean, default: false },
  isEligible: { type: Boolean, default: false },
  
  // Document Uploads
  documents: {
    aadhaarCard: {
      filename: String,
      path: String,
      uploadedAt: Date,
      verified: { type: Boolean, default: false }
    },
    voterIdCard: {
      filename: String,
      path: String,
      uploadedAt: Date,
      verified: { type: Boolean, default: false }
    },
    profilePhoto: {
      filename: String,
      path: String,
      uploadedAt: Date
    }
  },
  
  // Biometric Data
  faceEmbedding: {
    type: [Number], // Face recognition embedding vector
    select: false
  },
  voicePrint: {
    type: [Number], // Voice recognition data
    select: false
  },
  
  // Security
  loginAttempts: { type: Number, default: 0 },
  lockUntil: Date,
  lastLogin: Date,
  ipAddresses: [String],
  
  // OTP Management
  emailOTP: {
    code: String,
    expiresAt: Date,
    attempts: { type: Number, default: 0 }
  },
  phoneOTP: {
    code: String,
    expiresAt: Date,
    attempts: { type: Number, default: 0 }
  },
  
  // Password Reset
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  
  // Voting History (anonymized)
  votingHistory: [{
    electionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Election' },
    votedAt: Date,
    sessionId: String,
    verificationStatus: {
      faceMatch: Boolean,
      voiceCheck: Boolean,
      documentCheck: Boolean
    }
  }],
  
  // Account Status
  isActive: { type: Boolean, default: true },
  isBlocked: { type: Boolean, default: false },
  blockedReason: String,
  blockedAt: Date,
  
  // Metadata
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastModifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
});

// Indexes for performance
userSchema.index({ email: 1 });
userSchema.index({ aadhaarNumber: 1 });
userSchema.index({ voterIdNumber: 1 });
userSchema.index({ 'address.constituency': 1 });
userSchema.index({ 'address.state': 1, 'address.district': 1 });
userSchema.index({ role: 1 });
userSchema.index({ isEligible: 1, isActive: 1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware
userSchema.pre('save', async function(next) {
  // Hash password if modified
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Update timestamp on save
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateOTP = function(type = 'email') {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  
  if (type === 'email') {
    this.emailOTP = { code: otp, expiresAt, attempts: 0 };
  } else {
    this.phoneOTP = { code: otp, expiresAt, attempts: 0 };
  }
  
  return otp;
};

userSchema.methods.verifyOTP = function(otp, type = 'email') {
  const otpData = type === 'email' ? this.emailOTP : this.phoneOTP;
  
  if (!otpData || !otpData.code || otpData.expiresAt < new Date()) {
    return false;
  }
  
  if (otpData.attempts >= 3) {
    return false;
  }
  
  if (otpData.code === otp) {
    // Clear OTP after successful verification
    if (type === 'email') {
      this.emailOTP = undefined;
      this.isEmailVerified = true;
    } else {
      this.phoneOTP = undefined;
      this.isPhoneVerified = true;
    }
    return true;
  } else {
    otpData.attempts += 1;
    return false;
  }
};

userSchema.methods.generatePasswordResetToken = function() {
  const resetToken = crypto.randomBytes(20).toString('hex');
  
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');
  
  this.resetPasswordExpire = Date.now() + 30 * 60 * 1000; // 30 minutes
  
  return resetToken;
};

userSchema.methods.incrementLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 15 minutes
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 15 * 60 * 1000 };
  }
  
  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

module.exports = mongoose.model('User', userSchema);
