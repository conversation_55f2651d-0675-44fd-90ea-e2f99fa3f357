const express = require('express');
const { body } = require('express-validator');
const {
  getAvailableElections,
  getElectionForVoting,
  startVotingSession,
  verifyFaceDuringVoting,
  verifyVoiceDuringVoting,
  castVote,
  getVotingHistory
} = require('../controllers/votingController');
const { protect, checkVotingEligibility, sensitiveOperationLimit } = require('../middleware/auth');

const router = express.Router();

// All voting routes require authentication
router.use(protect);

// Validation rules for casting vote
const castVoteValidation = [
  body('candidateId')
    .isMongoId()
    .withMessage('Invalid candidate ID'),
  
  body('sessionId')
    .isUUID()
    .withMessage('Invalid session ID'),
  
  body('sessionStartTime')
    .isISO8601()
    .withMessage('Invalid session start time'),
  
  body('faceVerification.verified')
    .optional()
    .isBoolean()
    .withMessage('Face verification status must be boolean'),
  
  body('faceVerification.confidence')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Face verification confidence must be between 0 and 1'),
  
  body('voiceVerification.verified')
    .optional()
    .isBoolean()
    .withMessage('Voice verification status must be boolean'),
  
  body('voiceVerification.confidence')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Voice verification confidence must be between 0 and 1')
];

// Get available elections for the user
router.get('/elections', getAvailableElections);

// Get specific election details for voting
router.get('/elections/:electionId', getElectionForVoting);

// Start a voting session
router.post(
  '/elections/:electionId/start-session',
  checkVotingEligibility,
  sensitiveOperationLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  startVotingSession
);

// Verify face during voting
router.post(
  '/elections/:electionId/verify-face',
  checkVotingEligibility,
  sensitiveOperationLimit(5, 10 * 60 * 1000), // 5 attempts per 10 minutes
  verifyFaceDuringVoting
);

// Verify voice during voting
router.post(
  '/elections/:electionId/verify-voice',
  checkVotingEligibility,
  sensitiveOperationLimit(5, 10 * 60 * 1000), // 5 attempts per 10 minutes
  verifyVoiceDuringVoting
);

// Cast vote
router.post(
  '/elections/:electionId/cast-vote',
  checkVotingEligibility,
  sensitiveOperationLimit(1, 60 * 60 * 1000), // 1 vote per hour (strict limit)
  castVoteValidation,
  castVote
);

// Get user's voting history
router.get('/history', getVotingHistory);

module.exports = router;
