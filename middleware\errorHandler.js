const logger = require('../utils/logger');
const AuditLog = require('../models/AuditLog');

// Custom error class
class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';

    Error.captureStackTrace(this, this.constructor);
  }
}

// Handle different types of errors
const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return new AppError(message, 400);
};

const handleDuplicateFieldsDB = (err) => {
  const value = err.errmsg.match(/(["'])(\\?.)*?\1/)[0];
  const message = `Duplicate field value: ${value}. Please use another value!`;
  return new AppError(message, 400);
};

const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  return new AppError(message, 400);
};

const handleJWTError = () =>
  new AppError('Invalid token. Please log in again!', 401);

const handleJWTExpiredError = () =>
  new AppError('Your token has expired! Please log in again.', 401);

const handleMulterError = (err) => {
  if (err.code === 'LIMIT_FILE_SIZE') {
    return new AppError('File too large. Maximum size is 10MB.', 400);
  }
  if (err.code === 'LIMIT_FILE_COUNT') {
    return new AppError('Too many files. Maximum is 5 files.', 400);
  }
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return new AppError('Unexpected file field.', 400);
  }
  return new AppError('File upload error.', 400);
};

// Send error response in development
const sendErrorDev = (err, req, res) => {
  // Log error details
  logger.error('Error in development:', {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    user: req.user ? req.user._id : 'anonymous'
  });

  // API error
  if (req.originalUrl.startsWith('/api')) {
    return res.status(err.statusCode).json({
      success: false,
      error: err,
      message: err.message,
      stack: err.stack
    });
  }

  // Rendered website error
  res.status(err.statusCode).render('error', {
    title: 'Something went wrong!',
    msg: err.message
  });
};

// Send error response in production
const sendErrorProd = async (err, req, res) => {
  // Create audit log for errors
  try {
    if (req.user) {
      await AuditLog.createLog({
        eventType: 'system_error',
        userId: req.user._id,
        userEmail: req.user.email,
        userName: req.user.name,
        userRole: req.user.role,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        sessionId: req.sessionID,
        action: `${req.method} ${req.originalUrl}`,
        description: `System error: ${err.message}`,
        success: false,
        errorMessage: err.message,
        errorCode: err.statusCode,
        context: {
          source: 'api',
          component: 'error_handler'
        }
      });
    }
  } catch (auditError) {
    logger.error('Failed to create audit log for error:', auditError);
  }

  // API error
  if (req.originalUrl.startsWith('/api')) {
    // Operational, trusted error: send message to client
    if (err.isOperational) {
      return res.status(err.statusCode).json({
        success: false,
        message: err.message
      });
    }

    // Programming or other unknown error: don't leak error details
    logger.error('Unexpected error in production:', {
      error: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      user: req.user ? req.user._id : 'anonymous'
    });

    return res.status(500).json({
      success: false,
      message: 'Something went wrong!'
    });
  }

  // Rendered website error
  if (err.isOperational) {
    return res.status(err.statusCode).render('error', {
      title: 'Something went wrong!',
      msg: err.message
    });
  }

  // Programming or other unknown error
  logger.error('Unexpected error in production:', {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    user: req.user ? req.user._id : 'anonymous'
  });

  res.status(err.statusCode).render('error', {
    title: 'Something went wrong!',
    msg: 'Please try again later.'
  });
};

// Global error handling middleware
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, req, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    // Handle specific error types
    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();
    if (error.name === 'MulterError') error = handleMulterError(error);

    sendErrorProd(error, req, res);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  logger.error('Unhandled Promise Rejection:', {
    error: err.message,
    stack: err.stack,
    promise: promise
  });

  // Close server & exit process
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', {
    error: err.message,
    stack: err.stack
  });

  // Close server & exit process
  process.exit(1);
});

// Async error wrapper
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

// 404 handler
const notFound = (req, res, next) => {
  const message = `Not found - ${req.originalUrl}`;
  
  logger.warn('404 Not Found:', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    user: req.user ? req.user._id : 'anonymous'
  });

  if (req.originalUrl.startsWith('/api')) {
    return res.status(404).json({
      success: false,
      message: message
    });
  }

  res.status(404).render('404', {
    title: 'Page Not Found',
    msg: message
  });
};

// Security error handler
const handleSecurityError = (type, details, req) => {
  logger.security.suspiciousActivity(
    req.user ? req.user._id : 'unknown',
    type,
    details,
    req.ip
  );

  // Create audit log for security incidents
  if (req.user) {
    AuditLog.createLog({
      eventType: 'security_incident',
      userId: req.user._id,
      userEmail: req.user.email,
      userName: req.user.name,
      userRole: req.user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: type,
      description: `Security incident: ${type}`,
      success: false,
      metadata: details,
      context: {
        source: 'security',
        component: 'error_handler'
      }
    }).catch(error => {
      logger.error('Failed to create security audit log:', error);
    });
  }
};

// Rate limit error handler
const handleRateLimitError = (req, res) => {
  handleSecurityError('rate_limit_exceeded', {
    endpoint: req.originalUrl,
    method: req.method
  }, req);

  return res.status(429).json({
    success: false,
    message: 'Too many requests. Please try again later.',
    retryAfter: 60
  });
};

// Validation error formatter
const formatValidationErrors = (errors) => {
  const formatted = {};
  
  Object.keys(errors).forEach(key => {
    if (errors[key].message) {
      formatted[key] = errors[key].message;
    }
  });
  
  return formatted;
};

module.exports = {
  AppError,
  globalErrorHandler,
  catchAsync,
  notFound,
  handleSecurityError,
  handleRateLimitError,
  formatValidationErrors
};
