const nodemailer = require('nodemailer');
const logger = require('./logger');

// Create transporter
const createTransporter = () => {
  if (process.env.NODE_ENV === 'development') {
    // For development, use Ethereal Email (fake SMTP service)
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      auth: {
        user: '<EMAIL>',
        pass: 'ethereal.pass'
      }
    });
  }

  // For production, use real SMTP service
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send OTP email
const sendOTP = async (email, otp, name) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Voting System - Email Verification OTP',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Email Verification OTP</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .otp-box { background: #fff; border: 2px solid #007bff; padding: 20px; text-align: center; margin: 20px 0; }
            .otp-code { font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 5px; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🗳️ AI Voting System</h1>
              <p>Email Verification Required</p>
            </div>
            
            <div class="content">
              <h2>Hello ${name},</h2>
              <p>Thank you for registering with our AI-Enabled Voting System. To complete your registration, please verify your email address using the OTP below:</p>
              
              <div class="otp-box">
                <p>Your verification code is:</p>
                <div class="otp-code">${otp}</div>
                <p><small>This code will expire in 10 minutes</small></p>
              </div>
              
              <div class="warning">
                <strong>⚠️ Security Notice:</strong>
                <ul>
                  <li>Never share this OTP with anyone</li>
                  <li>Our team will never ask for your OTP over phone or email</li>
                  <li>If you didn't request this verification, please ignore this email</li>
                </ul>
              </div>
              
              <p>If you have any questions or need assistance, please contact our support team.</p>
              
              <p>Best regards,<br>AI Voting System Team</p>
            </div>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© 2024 AI Voting System. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Email OTP sent to ${email}. Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    } else {
      logger.info(`Email OTP sent to ${email}`);
    }

    return { success: true, messageId: info.messageId };
  } catch (error) {
    logger.error('Failed to send email OTP:', error);
    throw new Error('Failed to send email OTP');
  }
};

// Send password reset email
const sendPasswordReset = async (email, resetToken, name) => {
  try {
    const transporter = createTransporter();
    const resetURL = `${process.env.CLIENT_URL}/reset-password/${resetToken}`;

    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Voting System - Password Reset Request',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset Request</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            .warning { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔒 Password Reset Request</h1>
              <p>AI Voting System</p>
            </div>
            
            <div class="content">
              <h2>Hello ${name},</h2>
              <p>We received a request to reset your password for your AI Voting System account.</p>
              
              <p>Click the button below to reset your password:</p>
              
              <div style="text-align: center;">
                <a href="${resetURL}" class="button">Reset Password</a>
              </div>
              
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;">${resetURL}</p>
              
              <div class="warning">
                <strong>⚠️ Important:</strong>
                <ul>
                  <li>This link will expire in 30 minutes</li>
                  <li>If you didn't request this reset, please ignore this email</li>
                  <li>Your password will remain unchanged until you create a new one</li>
                </ul>
              </div>
              
              <p>If you continue to have problems, please contact our support team.</p>
              
              <p>Best regards,<br>AI Voting System Team</p>
            </div>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© 2024 AI Voting System. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Password reset email sent to ${email}. Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    } else {
      logger.info(`Password reset email sent to ${email}`);
    }

    return { success: true, messageId: info.messageId };
  } catch (error) {
    logger.error('Failed to send password reset email:', error);
    throw new Error('Failed to send password reset email');
  }
};

// Send welcome email
const sendWelcomeEmail = async (email, name) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Welcome to AI Voting System',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to AI Voting System</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            .feature { background: white; padding: 15px; margin: 10px 0; border-left: 4px solid #28a745; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Welcome to AI Voting System!</h1>
              <p>Your account has been successfully verified</p>
            </div>
            
            <div class="content">
              <h2>Hello ${name},</h2>
              <p>Congratulations! Your account has been successfully verified and you're now ready to participate in secure, AI-enabled voting.</p>
              
              <h3>What's Next?</h3>
              
              <div class="feature">
                <h4>📋 Complete Your Profile</h4>
                <p>Upload your identity documents for verification to become eligible for voting.</p>
              </div>
              
              <div class="feature">
                <h4>🤖 AI Verification</h4>
                <p>Complete face recognition setup for enhanced security during voting.</p>
              </div>
              
              <div class="feature">
                <h4>🗳️ Participate in Elections</h4>
                <p>Once verified, you can participate in active elections in your constituency.</p>
              </div>
              
              <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
              
              <p>Thank you for choosing our secure voting platform!</p>
              
              <p>Best regards,<br>AI Voting System Team</p>
            </div>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© 2024 AI Voting System. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Welcome email sent to ${email}. Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    } else {
      logger.info(`Welcome email sent to ${email}`);
    }

    return { success: true, messageId: info.messageId };
  } catch (error) {
    logger.error('Failed to send welcome email:', error);
    throw new Error('Failed to send welcome email');
  }
};

// Send election notification
const sendElectionNotification = async (email, name, electionTitle, startDate) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: `New Election: ${electionTitle}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Election Notification</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #6f42c1; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .election-info { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🗳️ New Election Available</h1>
              <p>AI Voting System</p>
            </div>
            
            <div class="content">
              <h2>Hello ${name},</h2>
              <p>A new election is now available for your constituency:</p>
              
              <div class="election-info">
                <h3>${electionTitle}</h3>
                <p><strong>Start Date:</strong> ${new Date(startDate).toLocaleDateString()}</p>
                <p><strong>Start Time:</strong> ${new Date(startDate).toLocaleTimeString()}</p>
              </div>
              
              <p>Make sure you're ready to vote by ensuring:</p>
              <ul>
                <li>✅ Your profile is complete</li>
                <li>✅ All verifications are done</li>
                <li>✅ You have a stable internet connection</li>
                <li>✅ Your camera and microphone are working</li>
              </ul>
              
              <p>Thank you for participating in the democratic process!</p>
              
              <p>Best regards,<br>AI Voting System Team</p>
            </div>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© 2024 AI Voting System. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Election notification sent to ${email}. Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    } else {
      logger.info(`Election notification sent to ${email}`);
    }

    return { success: true, messageId: info.messageId };
  } catch (error) {
    logger.error('Failed to send election notification:', error);
    throw new Error('Failed to send election notification');
  }
};

module.exports = {
  sendOTP,
  sendPasswordReset,
  sendWelcomeEmail,
  sendElectionNotification
};
