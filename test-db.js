require('dotenv').config();
const mongoose = require('mongoose');

console.log('Testing MongoDB connection...');
console.log('MongoDB URI:', process.env.MONGODB_URI);

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/voting-system', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✓ MongoDB Connected: ${conn.connection.host}`);
    process.exit(0);
  } catch (error) {
    console.error('✗ Database connection failed:', error.message);
    console.log('\nTrying to start MongoDB...');
    console.log('If MongoDB is not installed, you can:');
    console.log('1. Install MongoDB Community Edition');
    console.log('2. Use MongoDB Atlas (cloud)');
    console.log('3. Use Docker: docker run -d -p 27017:27017 mongo');
    process.exit(1);
  }
};

connectDB();
