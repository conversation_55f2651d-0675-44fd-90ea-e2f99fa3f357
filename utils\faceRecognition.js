const cv = require('opencv4nodejs');
const tf = require('@tensorflow/tfjs-node');
const path = require('path');
const fs = require('fs');
const logger = require('./logger');

class FaceRecognitionService {
  constructor() {
    this.faceClassifier = null;
    this.faceNetModel = null;
    this.isInitialized = false;
    this.threshold = parseFloat(process.env.FACE_RECOGNITION_THRESHOLD) || 0.6;
  }

  // Initialize the face recognition service
  async initialize() {
    try {
      // Load OpenCV face classifier
      const classifierPath = path.join(__dirname, '../models/haarcascade_frontalface_alt.xml');
      if (fs.existsSync(classifierPath)) {
        this.faceClassifier = new cv.CascadeClassifier(classifierPath);
      } else {
        logger.warn('Face classifier not found, using default OpenCV classifier');
        this.faceClassifier = new cv.CascadeClassifier(cv.HAAR_FRONTALFACE_ALT2);
      }

      // Load FaceNet model for face embeddings
      try {
        const modelPath = path.join(__dirname, '../models/facenet/model.json');
        if (fs.existsSync(modelPath)) {
          this.faceNetModel = await tf.loadLayersModel(`file://${modelPath}`);
          logger.info('FaceNet model loaded successfully');
        } else {
          logger.warn('FaceNet model not found, using simplified face recognition');
        }
      } catch (error) {
        logger.warn('Failed to load FaceNet model:', error.message);
      }

      this.isInitialized = true;
      logger.info('Face recognition service initialized');
    } catch (error) {
      logger.error('Failed to initialize face recognition service:', error);
      throw new Error('Face recognition initialization failed');
    }
  }

  // Detect faces in an image
  async detectFaces(imagePath) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Read image
      const image = cv.imread(imagePath);
      const grayImage = image.bgrToGray();

      // Detect faces
      const faces = this.faceClassifier.detectMultiScale(grayImage, {
        scaleFactor: 1.1,
        minNeighbors: 3,
        minSize: new cv.Size(30, 30)
      });

      logger.info(`Detected ${faces.objects.length} faces in image`);

      return {
        success: true,
        faceCount: faces.objects.length,
        faces: faces.objects.map(face => ({
          x: face.x,
          y: face.y,
          width: face.width,
          height: face.height,
          confidence: face.confidence || 1.0
        })),
        imageSize: {
          width: image.cols,
          height: image.rows
        }
      };
    } catch (error) {
      logger.error('Face detection failed:', error);
      return {
        success: false,
        error: error.message,
        faceCount: 0,
        faces: []
      };
    }
  }

  // Extract face embedding from image
  async extractFaceEmbedding(imagePath, faceRegion = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Read and preprocess image
      const image = cv.imread(imagePath);
      let faceImage;

      if (faceRegion) {
        // Extract face region
        const rect = new cv.Rect(
          faceRegion.x,
          faceRegion.y,
          faceRegion.width,
          faceRegion.height
        );
        faceImage = image.getRegion(rect);
      } else {
        // Use entire image
        faceImage = image;
      }

      // Resize to standard size (160x160 for FaceNet)
      const resizedFace = faceImage.resize(160, 160);
      const normalizedFace = resizedFace.convertTo(cv.CV_32F, 1.0 / 255.0);

      if (this.faceNetModel) {
        // Use FaceNet model for embedding
        const tensor = tf.browser.fromPixels(normalizedFace.getDataAsArray());
        const expandedTensor = tensor.expandDims(0);
        const embedding = await this.faceNetModel.predict(expandedTensor);
        const embeddingArray = await embedding.data();
        
        tensor.dispose();
        expandedTensor.dispose();
        embedding.dispose();

        return {
          success: true,
          embedding: Array.from(embeddingArray),
          method: 'facenet'
        };
      } else {
        // Use simplified embedding (histogram-based)
        const embedding = this.createSimpleEmbedding(normalizedFace);
        return {
          success: true,
          embedding: embedding,
          method: 'histogram'
        };
      }
    } catch (error) {
      logger.error('Face embedding extraction failed:', error);
      return {
        success: false,
        error: error.message,
        embedding: null
      };
    }
  }

  // Create simple embedding using histogram
  createSimpleEmbedding(image) {
    try {
      // Convert to grayscale if needed
      const grayImage = image.channels === 3 ? image.bgrToGray() : image;
      
      // Calculate histogram
      const hist = cv.calcHist([grayImage], [0], new cv.Mat(), [256], [0, 256]);
      const histArray = hist.getDataAsArray().flat();
      
      // Normalize histogram
      const sum = histArray.reduce((a, b) => a + b, 0);
      const normalizedHist = histArray.map(val => val / sum);
      
      // Add some spatial information (divide image into regions)
      const regions = this.extractRegionalFeatures(grayImage);
      
      return [...normalizedHist, ...regions];
    } catch (error) {
      logger.error('Simple embedding creation failed:', error);
      return new Array(256 + 16).fill(0); // Return zero vector as fallback
    }
  }

  // Extract regional features for simple embedding
  extractRegionalFeatures(grayImage) {
    try {
      const features = [];
      const height = grayImage.rows;
      const width = grayImage.cols;
      
      // Divide image into 4x4 grid and calculate mean intensity for each region
      for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
          const startY = Math.floor((i * height) / 4);
          const endY = Math.floor(((i + 1) * height) / 4);
          const startX = Math.floor((j * width) / 4);
          const endX = Math.floor(((j + 1) * width) / 4);
          
          const region = grayImage.getRegion(new cv.Rect(startX, startY, endX - startX, endY - startY));
          const mean = region.mean();
          features.push(mean.w); // Use the first channel (grayscale)
        }
      }
      
      return features;
    } catch (error) {
      logger.error('Regional feature extraction failed:', error);
      return new Array(16).fill(0);
    }
  }

  // Compare two face embeddings
  compareFaces(embedding1, embedding2) {
    try {
      if (!embedding1 || !embedding2 || embedding1.length !== embedding2.length) {
        return {
          success: false,
          similarity: 0,
          isMatch: false,
          error: 'Invalid embeddings'
        };
      }

      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(embedding1, embedding2);
      const isMatch = similarity >= this.threshold;

      return {
        success: true,
        similarity: similarity,
        isMatch: isMatch,
        threshold: this.threshold,
        confidence: similarity
      };
    } catch (error) {
      logger.error('Face comparison failed:', error);
      return {
        success: false,
        similarity: 0,
        isMatch: false,
        error: error.message
      };
    }
  }

  // Calculate cosine similarity between two vectors
  cosineSimilarity(vec1, vec2) {
    try {
      let dotProduct = 0;
      let norm1 = 0;
      let norm2 = 0;

      for (let i = 0; i < vec1.length; i++) {
        dotProduct += vec1[i] * vec2[i];
        norm1 += vec1[i] * vec1[i];
        norm2 += vec2[i] * vec2[i];
      }

      norm1 = Math.sqrt(norm1);
      norm2 = Math.sqrt(norm2);

      if (norm1 === 0 || norm2 === 0) {
        return 0;
      }

      return dotProduct / (norm1 * norm2);
    } catch (error) {
      logger.error('Cosine similarity calculation failed:', error);
      return 0;
    }
  }

  // Perform liveness detection
  async performLivenessDetection(imagePath) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const image = cv.imread(imagePath);
      
      // Simple liveness checks
      const checks = {
        brightness: this.checkBrightness(image),
        blur: this.checkBlur(image),
        colorVariation: this.checkColorVariation(image),
        faceSize: await this.checkFaceSize(imagePath)
      };

      // Calculate overall liveness score
      const scores = Object.values(checks).map(check => check.score);
      const averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;
      
      const isLive = averageScore >= 0.7; // Threshold for liveness

      return {
        success: true,
        isLive: isLive,
        confidence: averageScore,
        checks: checks,
        threshold: 0.7
      };
    } catch (error) {
      logger.error('Liveness detection failed:', error);
      return {
        success: false,
        isLive: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  // Check image brightness
  checkBrightness(image) {
    try {
      const grayImage = image.bgrToGray();
      const mean = grayImage.mean();
      const brightness = mean.w;
      
      // Good brightness range: 50-200
      let score = 1.0;
      if (brightness < 30 || brightness > 220) {
        score = 0.3;
      } else if (brightness < 50 || brightness > 200) {
        score = 0.7;
      }

      return {
        score: score,
        value: brightness,
        status: score >= 0.7 ? 'good' : 'poor'
      };
    } catch (error) {
      return { score: 0, value: 0, status: 'error' };
    }
  }

  // Check image blur
  checkBlur(image) {
    try {
      const grayImage = image.bgrToGray();
      const laplacian = grayImage.laplacian(cv.CV_64F);
      const variance = laplacian.stdDev().w ** 2;
      
      // Higher variance indicates less blur
      let score = Math.min(variance / 1000, 1.0);
      
      return {
        score: score,
        value: variance,
        status: score >= 0.5 ? 'good' : 'blurry'
      };
    } catch (error) {
      return { score: 0, value: 0, status: 'error' };
    }
  }

  // Check color variation
  checkColorVariation(image) {
    try {
      const channels = image.split();
      let totalVariation = 0;
      
      for (const channel of channels) {
        const stdDev = channel.stdDev();
        totalVariation += stdDev.w;
      }
      
      const averageVariation = totalVariation / channels.length;
      let score = Math.min(averageVariation / 50, 1.0);
      
      return {
        score: score,
        value: averageVariation,
        status: score >= 0.5 ? 'good' : 'low_variation'
      };
    } catch (error) {
      return { score: 0, value: 0, status: 'error' };
    }
  }

  // Check face size
  async checkFaceSize(imagePath) {
    try {
      const faceDetection = await this.detectFaces(imagePath);
      
      if (!faceDetection.success || faceDetection.faceCount === 0) {
        return { score: 0, value: 0, status: 'no_face' };
      }

      if (faceDetection.faceCount > 1) {
        return { score: 0.3, value: faceDetection.faceCount, status: 'multiple_faces' };
      }

      const face = faceDetection.faces[0];
      const faceArea = face.width * face.height;
      const imageArea = faceDetection.imageSize.width * faceDetection.imageSize.height;
      const faceRatio = faceArea / imageArea;
      
      // Good face ratio: 0.1 to 0.6 of image
      let score = 1.0;
      if (faceRatio < 0.05 || faceRatio > 0.8) {
        score = 0.3;
      } else if (faceRatio < 0.1 || faceRatio > 0.6) {
        score = 0.7;
      }

      return {
        score: score,
        value: faceRatio,
        status: score >= 0.7 ? 'good' : 'poor_size'
      };
    } catch (error) {
      return { score: 0, value: 0, status: 'error' };
    }
  }

  // Verify face against stored embedding
  async verifyFace(imagePath, storedEmbedding) {
    try {
      // Detect faces first
      const faceDetection = await this.detectFaces(imagePath);
      
      if (!faceDetection.success || faceDetection.faceCount === 0) {
        return {
          success: false,
          isMatch: false,
          confidence: 0,
          error: 'No face detected'
        };
      }

      if (faceDetection.faceCount > 1) {
        return {
          success: false,
          isMatch: false,
          confidence: 0,
          error: 'Multiple faces detected'
        };
      }

      // Extract embedding from the detected face
      const face = faceDetection.faces[0];
      const embeddingResult = await this.extractFaceEmbedding(imagePath, face);
      
      if (!embeddingResult.success) {
        return {
          success: false,
          isMatch: false,
          confidence: 0,
          error: 'Failed to extract face embedding'
        };
      }

      // Compare with stored embedding
      const comparison = this.compareFaces(embeddingResult.embedding, storedEmbedding);
      
      // Perform liveness detection
      const livenessResult = await this.performLivenessDetection(imagePath);

      return {
        success: true,
        isMatch: comparison.isMatch && livenessResult.isLive,
        confidence: comparison.similarity,
        faceMatch: comparison.isMatch,
        livenessCheck: livenessResult.isLive,
        livenessConfidence: livenessResult.confidence,
        details: {
          faceDetection: faceDetection,
          comparison: comparison,
          liveness: livenessResult
        }
      };
    } catch (error) {
      logger.error('Face verification failed:', error);
      return {
        success: false,
        isMatch: false,
        confidence: 0,
        error: error.message
      };
    }
  }
}

// Export singleton instance
const faceRecognitionService = new FaceRecognitionService();
module.exports = faceRecognitionService;
