const express = require('express');
const {
  uploadMiddleware,
  uploadDocuments,
  verifyFaceAgainstDocuments,
  getUserDocuments,
  deleteDocument
} = require('../controllers/uploadController');
const { protect, sensitiveOperationLimit } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Upload documents
router.post(
  '/documents',
  sensitiveOperationLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  uploadMiddleware,
  uploadDocuments
);

// Verify face against documents
router.post(
  '/verify-face',
  sensitiveOperationLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  verifyFaceAgainstDocuments
);

// Get user documents
router.get('/documents', getUserDocuments);

// Delete specific document
router.delete(
  '/documents/:documentType',
  sensitiveOperationLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  deleteDocument
);

module.exports = router;
