const crypto = require('crypto');
const { validationResult } = require('express-validator');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { generateToken, generateRefreshToken } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const emailService = require('../utils/emailService');
const smsService = require('../utils/smsService');

// Register user
const register = catchAsync(async (req, res, next) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const {
    name,
    email,
    phone,
    aadhaarNumber,
    voterIdNumber,
    password,
    address
  } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [
      { email },
      { aadhaarNumber },
      { voterIdNumber },
      { phone }
    ]
  });

  if (existingUser) {
    let field = 'email';
    if (existingUser.aadhaarNumber === aadhaarNumber) field = 'Aadhaar number';
    else if (existingUser.voterIdNumber === voterIdNumber) field = 'Voter ID';
    else if (existingUser.phone === phone) field = 'Phone number';

    return next(new AppError(`User with this ${field} already exists`, 400));
  }

  // Create user
  const user = await User.create({
    name,
    email,
    phone,
    aadhaarNumber,
    voterIdNumber,
    password,
    address,
    role: 'voter'
  });

  // Generate OTPs
  const emailOTP = user.generateOTP('email');
  const phoneOTP = user.generateOTP('phone');
  await user.save();

  // Send OTPs
  try {
    await emailService.sendOTP(email, emailOTP, name);
    await smsService.sendOTP(phone, phoneOTP);
    logger.info(`OTPs sent to ${email} and ${phone}`);
  } catch (error) {
    logger.error('Failed to send OTP:', error);
    // Don't fail registration if OTP sending fails
  }

  // Create audit log
  await AuditLog.createLog({
    eventType: 'user_registration',
    userId: user._id,
    userEmail: user.email,
    userName: user.name,
    userRole: user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'user_registration',
    description: `New user registered: ${user.name}`,
    success: true,
    context: {
      source: 'web',
      component: 'auth_controller'
    }
  });

  logger.info(`New user registered: ${user.email}`);

  res.status(201).json({
    success: true,
    message: 'User registered successfully. Please verify your email and phone number.',
    data: {
      userId: user._id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      verificationRequired: {
        email: !user.isEmailVerified,
        phone: !user.isPhoneVerified
      }
    }
  });
});

// Login user
const login = catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { email, password } = req.body;

  // Find user and include password
  const user = await User.findOne({ email }).select('+password');

  if (!user) {
    logger.security.loginAttempt(email, req.ip, false, 'User not found');
    return next(new AppError('Invalid credentials', 401));
  }

  // Check if account is locked
  if (user.isLocked) {
    logger.security.loginAttempt(email, req.ip, false, 'Account locked');
    return next(new AppError('Account is temporarily locked due to multiple failed login attempts', 423));
  }

  // Check if account is blocked
  if (user.isBlocked) {
    logger.security.loginAttempt(email, req.ip, false, 'Account blocked');
    return next(new AppError('Account is blocked. Please contact support.', 403));
  }

  // Check if account is active
  if (!user.isActive) {
    logger.security.loginAttempt(email, req.ip, false, 'Account inactive');
    return next(new AppError('Account is deactivated. Please contact support.', 403));
  }

  // Check password
  const isPasswordCorrect = await user.comparePassword(password);

  if (!isPasswordCorrect) {
    // Increment login attempts
    await user.incrementLoginAttempts();
    logger.security.loginAttempt(email, req.ip, false, 'Invalid password');
    return next(new AppError('Invalid credentials', 401));
  }

  // Reset login attempts on successful login
  if (user.loginAttempts > 0) {
    await user.resetLoginAttempts();
  }

  // Update last login and IP
  user.lastLogin = new Date();
  if (!user.ipAddresses.includes(req.ip)) {
    user.ipAddresses.push(req.ip);
    // Keep only last 10 IPs
    if (user.ipAddresses.length > 10) {
      user.ipAddresses = user.ipAddresses.slice(-10);
    }
  }
  await user.save();

  // Generate tokens
  const token = generateToken(user._id, user.role);
  const refreshToken = generateRefreshToken(user._id);

  // Set session data
  req.session.userId = user._id.toString();
  req.session.userRole = user.role;

  // Create audit log
  await AuditLog.createLog({
    eventType: 'user_login',
    userId: user._id,
    userEmail: user.email,
    userName: user.name,
    userRole: user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'user_login',
    description: `User logged in: ${user.name}`,
    success: true,
    context: {
      source: 'web',
      component: 'auth_controller'
    }
  });

  logger.security.loginAttempt(email, req.ip, true);

  // Remove sensitive data
  user.password = undefined;
  user.aadhaarNumber = undefined;
  user.faceEmbedding = undefined;
  user.voicePrint = undefined;

  res.status(200).json({
    success: true,
    message: 'Login successful',
    data: {
      user,
      token,
      refreshToken,
      expiresIn: '24h'
    }
  });
});

// Logout user
const logout = catchAsync(async (req, res, next) => {
  // Create audit log
  if (req.user) {
    await AuditLog.createLog({
      eventType: 'user_logout',
      userId: req.user._id,
      userEmail: req.user.email,
      userName: req.user.name,
      userRole: req.user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'user_logout',
      description: `User logged out: ${req.user.name}`,
      success: true,
      context: {
        source: 'web',
        component: 'auth_controller'
      }
    });

    logger.info(`User logged out: ${req.user.email}`);
  }

  // Destroy session
  req.session.destroy((err) => {
    if (err) {
      logger.error('Session destruction error:', err);
    }
  });

  res.status(200).json({
    success: true,
    message: 'Logout successful'
  });
});

// Verify email OTP
const verifyEmailOTP = catchAsync(async (req, res, next) => {
  const { email, otp } = req.body;

  const user = await User.findOne({ email });
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  if (user.isEmailVerified) {
    return res.status(200).json({
      success: true,
      message: 'Email already verified'
    });
  }

  const isValidOTP = user.verifyOTP(otp, 'email');
  if (!isValidOTP) {
    return next(new AppError('Invalid or expired OTP', 400));
  }

  await user.save();

  logger.info(`Email verified for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Email verified successfully'
  });
});

// Verify phone OTP
const verifyPhoneOTP = catchAsync(async (req, res, next) => {
  const { phone, otp } = req.body;

  const user = await User.findOne({ phone });
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  if (user.isPhoneVerified) {
    return res.status(200).json({
      success: true,
      message: 'Phone already verified'
    });
  }

  const isValidOTP = user.verifyOTP(otp, 'phone');
  if (!isValidOTP) {
    return next(new AppError('Invalid or expired OTP', 400));
  }

  await user.save();

  logger.info(`Phone verified for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Phone verified successfully'
  });
});

// Resend OTP
const resendOTP = catchAsync(async (req, res, next) => {
  const { email, type } = req.body; // type: 'email' or 'phone'

  const user = await User.findOne({ email });
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  if (type === 'email' && user.isEmailVerified) {
    return next(new AppError('Email already verified', 400));
  }

  if (type === 'phone' && user.isPhoneVerified) {
    return next(new AppError('Phone already verified', 400));
  }

  // Generate new OTP
  const otp = user.generateOTP(type);
  await user.save();

  // Send OTP
  try {
    if (type === 'email') {
      await emailService.sendOTP(user.email, otp, user.name);
    } else {
      await smsService.sendOTP(user.phone, otp);
    }
  } catch (error) {
    logger.error('Failed to resend OTP:', error);
    return next(new AppError('Failed to send OTP. Please try again.', 500));
  }

  res.status(200).json({
    success: true,
    message: `OTP sent to ${type === 'email' ? 'email' : 'phone'} successfully`
  });
});

// Get current user profile
const getProfile = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id)
    .select('-password -aadhaarNumber -faceEmbedding -voicePrint')
    .populate('votingHistory.electionId', 'title type');

  res.status(200).json({
    success: true,
    data: { user }
  });
});

// Update user profile
const updateProfile = catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const allowedFields = ['name', 'address'];
  const updates = {};

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.user._id,
    updates,
    { new: true, runValidators: true }
  ).select('-password -aadhaarNumber -faceEmbedding -voicePrint');

  logger.info(`Profile updated for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: { user }
  });
});

// Change password
const changePassword = catchAsync(async (req, res, next) => {
  const { currentPassword, newPassword } = req.body;

  const user = await User.findById(req.user._id).select('+password');

  // Check current password
  const isCurrentPasswordCorrect = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordCorrect) {
    return next(new AppError('Current password is incorrect', 400));
  }

  // Update password
  user.password = newPassword;
  await user.save();

  // Create audit log
  await AuditLog.createLog({
    eventType: 'password_change',
    userId: user._id,
    userEmail: user.email,
    userName: user.name,
    userRole: user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'password_change',
    description: 'User changed password',
    success: true,
    context: {
      source: 'web',
      component: 'auth_controller'
    }
  });

  logger.info(`Password changed for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Password changed successfully'
  });
});

// Forgot password
const forgotPassword = catchAsync(async (req, res, next) => {
  const { email } = req.body;

  const user = await User.findOne({ email });
  if (!user) {
    // Don't reveal if user exists or not
    return res.status(200).json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  }

  // Generate reset token
  const resetToken = user.generatePasswordResetToken();
  await user.save({ validateBeforeSave: false });

  try {
    // Send reset email
    await emailService.sendPasswordReset(user.email, resetToken, user.name);

    logger.info(`Password reset requested for user: ${user.email}`);

    res.status(200).json({
      success: true,
      message: 'Password reset link sent to email'
    });
  } catch (error) {
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save({ validateBeforeSave: false });

    logger.error('Failed to send password reset email:', error);
    return next(new AppError('Failed to send password reset email', 500));
  }
});

// Reset password
const resetPassword = catchAsync(async (req, res, next) => {
  const { token } = req.params;
  const { password } = req.body;

  // Hash token and find user
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  const user = await User.findOne({
    resetPasswordToken: hashedToken,
    resetPasswordExpire: { $gt: Date.now() }
  });

  if (!user) {
    return next(new AppError('Token is invalid or has expired', 400));
  }

  // Set new password
  user.password = password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpire = undefined;
  await user.save();

  logger.info(`Password reset completed for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Password reset successful'
  });
});

module.exports = {
  register,
  login,
  logout,
  verifyEmailOTP,
  verifyPhoneOTP,
  resendOTP,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword
};
