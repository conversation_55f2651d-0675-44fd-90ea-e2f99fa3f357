const crypto = require('crypto');
const { validationResult } = require('express-validator');
const User = require('../models/User');
const Election = require('../models/Election');
const Vote = require('../models/Vote');
const AuditLog = require('../models/AuditLog');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const faceRecognition = require('../utils/faceRecognition');
const voiceDetection = require('../utils/voiceDetection');

// Get available elections for user
const getAvailableElections = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Find elections that are active and user is eligible for
  const elections = await Election.find({
    status: 'active',
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() },
    $or: [
      { 'geographicCoverage.constituencies': user.address.constituency },
      { 'geographicCoverage.states': user.address.state },
      { scope: 'national' }
    ]
  }).select('title description type startDate endDate candidates ballot');

  // Filter elections user hasn't voted in (if multiple votes not allowed)
  const availableElections = [];
  
  for (const election of elections) {
    const eligibilityCheck = election.canUserVote(user);
    if (eligibilityCheck.canVote) {
      availableElections.push({
        ...election.toObject(),
        eligibilityStatus: eligibilityCheck
      });
    }
  }

  res.status(200).json({
    success: true,
    data: {
      elections: availableElections,
      userEligibility: {
        isEligible: user.isEligible,
        verificationStatus: {
          email: user.isEmailVerified,
          phone: user.isPhoneVerified,
          document: user.isDocumentVerified,
          face: user.isFaceVerified
        }
      }
    }
  });
});

// Get election details for voting
const getElectionForVoting = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const user = await User.findById(req.user._id);

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user can vote in this election
  const eligibilityCheck = election.canUserVote(user);
  if (!eligibilityCheck.canVote) {
    return next(new AppError(eligibilityCheck.reason, 403));
  }

  // Return election details with candidates
  const electionData = {
    _id: election._id,
    title: election.title,
    description: election.description,
    type: election.type,
    startDate: election.startDate,
    endDate: election.endDate,
    ballot: election.ballot,
    candidates: election.candidates.filter(c => c.isActive).map(candidate => ({
      _id: candidate._id,
      name: candidate.name,
      party: candidate.party,
      photo: candidate.photo,
      biography: candidate.biography,
      constituency: candidate.constituency,
      position: candidate.position
    })),
    settings: {
      requireFaceVerification: election.settings.requireFaceVerification,
      requireVoiceVerification: election.settings.requireVoiceVerification,
      maxVotingDuration: election.settings.maxVotingDuration
    }
  };

  res.status(200).json({
    success: true,
    data: {
      election: electionData,
      userInfo: {
        constituency: user.address.constituency,
        state: user.address.state,
        district: user.address.district
      }
    }
  });
});

// Start voting session
const startVotingSession = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const user = await User.findById(req.user._id);

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user can vote
  const eligibilityCheck = election.canUserVote(user);
  if (!eligibilityCheck.canVote) {
    return next(new AppError(eligibilityCheck.reason, 403));
  }

  // Generate session ID
  const sessionId = crypto.randomUUID();
  const startTime = new Date();

  // Create audit log for session start
  await AuditLog.createLog({
    eventType: 'voting_session_started',
    userId: user._id,
    userEmail: user.email,
    userName: user.name,
    userRole: user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'start_voting_session',
    description: `Started voting session for election: ${election.title}`,
    success: true,
    context: {
      electionId: election._id,
      electionTitle: election.title,
      votingSessionId: sessionId,
      source: 'web',
      component: 'voting_controller'
    }
  });

  logger.info(`Voting session started: ${sessionId} for user: ${user.email} in election: ${election.title}`);

  res.status(200).json({
    success: true,
    data: {
      sessionId,
      startTime,
      maxDuration: election.settings.maxVotingDuration * 60 * 1000, // Convert to milliseconds
      verificationRequired: {
        face: election.settings.requireFaceVerification,
        voice: election.settings.requireVoiceVerification
      }
    }
  });
});

// Verify face during voting
const verifyFaceDuringVoting = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const { sessionId, faceImageData } = req.body;

  if (!sessionId || !faceImageData) {
    return next(new AppError('Session ID and face image data are required', 400));
  }

  const user = await User.findById(req.user._id);
  if (!user || !user.faceEmbedding) {
    return next(new AppError('User face data not found. Please complete face verification first.', 400));
  }

  try {
    // Save the face image temporarily
    const tempImagePath = `./uploads/temp/face_${sessionId}_${Date.now()}.jpg`;
    const base64Data = faceImageData.replace(/^data:image\/[a-z]+;base64,/, '');
    require('fs').writeFileSync(tempImagePath, base64Data, 'base64');

    // Verify face against stored embedding
    const verificationResult = await faceRecognition.verifyFace(tempImagePath, user.faceEmbedding);

    // Clean up temporary file
    require('fs').unlinkSync(tempImagePath);

    // Create audit log
    await AuditLog.createLog({
      eventType: 'face_verification',
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'verify_face_voting',
      description: `Face verification during voting session: ${sessionId}`,
      success: verificationResult.isMatch,
      metadata: {
        sessionId,
        confidence: verificationResult.confidence,
        faceMatch: verificationResult.faceMatch,
        livenessCheck: verificationResult.livenessCheck
      },
      context: {
        electionId,
        votingSessionId: sessionId,
        source: 'web',
        component: 'voting_controller'
      }
    });

    res.status(200).json({
      success: true,
      data: {
        verified: verificationResult.isMatch,
        confidence: verificationResult.confidence,
        details: {
          faceMatch: verificationResult.faceMatch,
          livenessCheck: verificationResult.livenessCheck,
          livenessConfidence: verificationResult.livenessConfidence
        }
      }
    });
  } catch (error) {
    logger.error('Face verification during voting failed:', error);
    return next(new AppError('Face verification failed', 500));
  }
});

// Verify voice during voting
const verifyVoiceDuringVoting = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const { sessionId, voiceAudioData } = req.body;

  if (!sessionId || !voiceAudioData) {
    return next(new AppError('Session ID and voice audio data are required', 400));
  }

  const user = await User.findById(req.user._id);

  try {
    // Save the audio temporarily
    const tempAudioPath = `./uploads/temp/voice_${sessionId}_${Date.now()}.wav`;
    const base64Data = voiceAudioData.replace(/^data:audio\/[a-z]+;base64,/, '');
    require('fs').writeFileSync(tempAudioPath, base64Data, 'base64');

    // Analyze voice for multiple speakers
    const voiceAnalysis = await voiceDetection.analyzeAudio(tempAudioPath);

    // If user has stored voice features, compare them
    let voiceMatch = true;
    let voiceConfidence = 0.8;
    
    if (user.voicePrint && user.voicePrint.length > 0) {
      const voiceFeatures = await voiceDetection.extractVoiceFeatures(tempAudioPath);
      if (voiceFeatures.success) {
        const comparison = voiceDetection.compareVoiceFeatures(voiceFeatures.features, user.voicePrint);
        voiceMatch = comparison.isMatch;
        voiceConfidence = comparison.confidence;
      }
    }

    // Clean up temporary file
    require('fs').unlinkSync(tempAudioPath);

    const verificationResult = {
      success: voiceAnalysis.success,
      voiceMatch: voiceMatch,
      multipleVoicesDetected: voiceAnalysis.multipleVoicesDetected,
      confidence: voiceConfidence,
      voiceQuality: voiceAnalysis.voiceAnalysis?.speechQuality || 0.5
    };

    // Create audit log
    await AuditLog.createLog({
      eventType: 'voice_verification',
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'verify_voice_voting',
      description: `Voice verification during voting session: ${sessionId}`,
      success: verificationResult.voiceMatch && !verificationResult.multipleVoicesDetected,
      metadata: {
        sessionId,
        ...verificationResult
      },
      context: {
        electionId,
        votingSessionId: sessionId,
        source: 'web',
        component: 'voting_controller'
      }
    });

    res.status(200).json({
      success: true,
      data: verificationResult
    });
  } catch (error) {
    logger.error('Voice verification during voting failed:', error);
    return next(new AppError('Voice verification failed', 500));
  }
});

// Cast vote
const castVote = catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { electionId } = req.params;
  const {
    candidateId,
    sessionId,
    sessionStartTime,
    faceVerification,
    voiceVerification
  } = req.body;

  const user = await User.findById(req.user._id);
  const election = await Election.findById(electionId);

  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Final eligibility check
  const eligibilityCheck = election.canUserVote(user);
  if (!eligibilityCheck.canVote) {
    return next(new AppError(eligibilityCheck.reason, 403));
  }

  // Find candidate
  const candidate = election.candidates.find(c => c._id.toString() === candidateId && c.isActive);
  if (!candidate) {
    return next(new AppError('Candidate not found', 404));
  }

  // Validate session
  const sessionDuration = (new Date() - new Date(sessionStartTime)) / 1000; // in seconds
  if (sessionDuration > election.settings.maxVotingDuration * 60) {
    return next(new AppError('Voting session expired', 400));
  }

  try {
    // Create voter hash for anonymization
    const voterHash = crypto.createHash('sha256')
      .update(user._id.toString() + election._id.toString() + Date.now().toString())
      .digest('hex');

    // Create vote record
    const vote = await Vote.create({
      electionId: election._id,
      candidateId: candidate._id,
      candidateName: candidate.name,
      party: candidate.party,
      geographic: {
        state: user.address.state,
        district: user.address.district,
        constituency: user.address.constituency,
        city: user.address.city,
        village: user.address.village,
        pincode: user.address.pincode
      },
      session: {
        sessionId,
        startTime: new Date(sessionStartTime),
        endTime: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        deviceFingerprint: req.get('X-Device-Fingerprint') || 'unknown'
      },
      verification: {
        faceVerification: {
          status: faceVerification?.verified || false,
          confidence: faceVerification?.confidence || 0,
          attempts: faceVerification?.attempts || 1,
          timestamp: new Date()
        },
        voiceVerification: {
          status: voiceVerification?.verified || false,
          multipleVoicesDetected: voiceVerification?.multipleVoicesDetected || false,
          voiceConfidence: voiceVerification?.confidence || 0,
          timestamp: new Date()
        },
        documentVerification: {
          status: user.isDocumentVerified,
          documentsChecked: ['aadhaar', 'voter_id'],
          timestamp: new Date()
        },
        livenessCheck: {
          status: faceVerification?.livenessCheck || false,
          confidence: faceVerification?.livenessConfidence || 0,
          timestamp: new Date()
        }
      },
      voterHash,
      isValid: true
    });

    // Update election results
    await election.updateResults({ candidateId: candidate._id });

    // Add to user's voting history (anonymized)
    user.votingHistory.push({
      electionId: election._id,
      votedAt: new Date(),
      sessionId,
      verificationStatus: {
        faceMatch: faceVerification?.verified || false,
        voiceCheck: voiceVerification?.verified || false,
        documentCheck: user.isDocumentVerified
      }
    });
    await user.save();

    // Create audit log
    await AuditLog.createLog({
      eventType: 'vote_cast',
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'cast_vote',
      description: `Vote cast in election: ${election.title}`,
      success: true,
      metadata: {
        votingSessionId: sessionId,
        candidateName: candidate.name,
        party: candidate.party.name,
        verificationStatus: vote.verification
      },
      context: {
        electionId: election._id,
        electionTitle: election.title,
        source: 'web',
        component: 'voting_controller'
      }
    });

    logger.election.voteCast(
      election._id,
      candidate._id,
      sessionId,
      vote.verification
    );

    // Check if vote is suspicious
    if (vote.security.isSuspicious) {
      logger.election.suspiciousVote(
        election._id,
        sessionId,
        vote.security.suspiciousReasons,
        vote.security.riskScore
      );
    }

    res.status(201).json({
      success: true,
      message: 'Vote cast successfully',
      data: {
        voteId: vote._id,
        sessionId,
        candidateName: candidate.name,
        party: candidate.party.name,
        timestamp: vote.castedAt,
        verificationStatus: {
          overall: vote.overallVerificationStatus,
          face: vote.verification.faceVerification.status,
          voice: vote.verification.voiceVerification.status,
          document: vote.verification.documentVerification.status,
          liveness: vote.verification.livenessCheck.status
        },
        securityStatus: {
          riskScore: vote.security.riskScore,
          flagged: vote.security.flaggedForReview
        }
      }
    });
  } catch (error) {
    logger.error('Vote casting failed:', error);
    return next(new AppError('Failed to cast vote', 500));
  }
});

// Get user's voting history
const getVotingHistory = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id)
    .populate('votingHistory.electionId', 'title type startDate endDate status')
    .select('votingHistory');

  res.status(200).json({
    success: true,
    data: {
      votingHistory: user.votingHistory.map(vote => ({
        election: vote.electionId,
        votedAt: vote.votedAt,
        sessionId: vote.sessionId,
        verificationStatus: vote.verificationStatus
      }))
    }
  });
});

module.exports = {
  getAvailableElections,
  getElectionForVoting,
  startVotingSession,
  verifyFaceDuringVoting,
  verifyVoiceDuringVoting,
  castVote,
  getVotingHistory
};
