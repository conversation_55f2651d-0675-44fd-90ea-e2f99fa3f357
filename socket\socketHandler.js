const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Election = require('../models/Election');
const Vote = require('../models/Vote');
const logger = require('../utils/logger');

// Store active connections
const activeConnections = new Map();
const electionRooms = new Map();

// Socket authentication middleware
const authenticateSocket = async (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return next(new Error('Authentication token required'));
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password -aadhaarNumber -faceEmbedding -voicePrint');
    
    if (!user || !user.isActive) {
      return next(new Error('Invalid or inactive user'));
    }

    socket.user = user;
    next();
  } catch (error) {
    logger.error('Socket authentication failed:', error);
    next(new Error('Authentication failed'));
  }
};

// Handle socket connections
const handleConnection = (io) => {
  // Apply authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket) => {
    logger.info(`User connected: ${socket.user.email} (${socket.id})`);
    
    // Store connection
    activeConnections.set(socket.id, {
      userId: socket.user._id,
      userEmail: socket.user.email,
      userRole: socket.user.role,
      connectedAt: new Date()
    });

    // Handle joining election rooms for real-time updates
    socket.on('join-election', async (data) => {
      try {
        const { electionId } = data;
        
        if (!electionId) {
          socket.emit('error', { message: 'Election ID is required' });
          return;
        }

        const election = await Election.findById(electionId);
        if (!election) {
          socket.emit('error', { message: 'Election not found' });
          return;
        }

        // Check if user has permission to view election data
        const hasPermission = election.administrators.some(
          admin => admin.userId.toString() === socket.user._id.toString()
        ) || ['admin', 'election_officer', 'analyst', 'observer'].includes(socket.user.role);

        if (!hasPermission) {
          socket.emit('error', { message: 'Not authorized to view election data' });
          return;
        }

        // Join election room
        socket.join(`election-${electionId}`);
        
        // Track room membership
        if (!electionRooms.has(electionId)) {
          electionRooms.set(electionId, new Set());
        }
        electionRooms.get(electionId).add(socket.id);

        logger.info(`User ${socket.user.email} joined election room: ${electionId}`);
        
        // Send initial election data
        const initialData = await getElectionRealTimeData(electionId);
        socket.emit('election-data', initialData);
        
        // Notify others in the room
        socket.to(`election-${electionId}`).emit('user-joined', {
          userId: socket.user._id,
          userName: socket.user.name,
          userRole: socket.user.role
        });
      } catch (error) {
        logger.error('Error joining election room:', error);
        socket.emit('error', { message: 'Failed to join election room' });
      }
    });

    // Handle leaving election rooms
    socket.on('leave-election', (data) => {
      try {
        const { electionId } = data;
        
        if (electionId) {
          socket.leave(`election-${electionId}`);
          
          // Remove from room tracking
          if (electionRooms.has(electionId)) {
            electionRooms.get(electionId).delete(socket.id);
            if (electionRooms.get(electionId).size === 0) {
              electionRooms.delete(electionId);
            }
          }

          logger.info(`User ${socket.user.email} left election room: ${electionId}`);
          
          // Notify others in the room
          socket.to(`election-${electionId}`).emit('user-left', {
            userId: socket.user._id,
            userName: socket.user.name
          });
        }
      } catch (error) {
        logger.error('Error leaving election room:', error);
      }
    });

    // Handle voting session monitoring
    socket.on('start-voting-session', (data) => {
      try {
        const { electionId, sessionId } = data;
        
        // Join voting session room for monitoring
        socket.join(`voting-session-${sessionId}`);
        
        logger.info(`Voting session started: ${sessionId} for user: ${socket.user.email}`);
        
        // Notify election administrators
        socket.to(`election-${electionId}`).emit('voting-session-started', {
          sessionId,
          userId: socket.user._id,
          userName: socket.user.name,
          timestamp: new Date()
        });
      } catch (error) {
        logger.error('Error starting voting session:', error);
      }
    });

    // Handle voting session updates
    socket.on('voting-session-update', (data) => {
      try {
        const { sessionId, status, verification } = data;
        
        // Emit to voting session room
        socket.to(`voting-session-${sessionId}`).emit('session-update', {
          sessionId,
          status,
          verification,
          timestamp: new Date()
        });
      } catch (error) {
        logger.error('Error updating voting session:', error);
      }
    });

    // Handle vote cast notification
    socket.on('vote-cast', async (data) => {
      try {
        const { electionId, sessionId } = data;
        
        // Update real-time election data
        const updatedData = await getElectionRealTimeData(electionId);
        
        // Notify all users in election room
        io.to(`election-${electionId}`).emit('election-data-update', updatedData);
        
        // Notify about new vote (without revealing voter identity)
        io.to(`election-${electionId}`).emit('new-vote', {
          electionId,
          timestamp: new Date(),
          totalVotes: updatedData.totalVotes
        });

        logger.info(`Vote cast notification sent for election: ${electionId}`);
      } catch (error) {
        logger.error('Error handling vote cast:', error);
      }
    });

    // Handle suspicious activity alerts
    socket.on('suspicious-activity', (data) => {
      try {
        const { electionId, sessionId, type, details } = data;
        
        // Notify election administrators immediately
        io.to(`election-${electionId}`).emit('security-alert', {
          type: 'suspicious_activity',
          sessionId,
          activityType: type,
          details,
          userId: socket.user._id,
          timestamp: new Date()
        });

        logger.warn(`Suspicious activity reported: ${type} in session: ${sessionId}`);
      } catch (error) {
        logger.error('Error handling suspicious activity:', error);
      }
    });

    // Handle system health monitoring
    socket.on('system-health-check', () => {
      try {
        if (['admin', 'super_admin'].includes(socket.user.role)) {
          const healthData = {
            activeConnections: activeConnections.size,
            activeElectionRooms: electionRooms.size,
            serverUptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            timestamp: new Date()
          };
          
          socket.emit('system-health', healthData);
        }
      } catch (error) {
        logger.error('Error checking system health:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User disconnected: ${socket.user.email} (${socket.id}) - Reason: ${reason}`);
      
      // Clean up connection tracking
      activeConnections.delete(socket.id);
      
      // Clean up election room tracking
      for (const [electionId, socketIds] of electionRooms.entries()) {
        if (socketIds.has(socket.id)) {
          socketIds.delete(socket.id);
          if (socketIds.size === 0) {
            electionRooms.delete(electionId);
          }
          
          // Notify others in the room
          socket.to(`election-${electionId}`).emit('user-left', {
            userId: socket.user._id,
            userName: socket.user.name
          });
        }
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Socket error for user ${socket.user.email}:`, error);
    });
  });

  // Periodic updates for active elections
  setInterval(async () => {
    try {
      for (const electionId of electionRooms.keys()) {
        const updatedData = await getElectionRealTimeData(electionId);
        io.to(`election-${electionId}`).emit('periodic-update', updatedData);
      }
    } catch (error) {
      logger.error('Error sending periodic updates:', error);
    }
  }, 30000); // Update every 30 seconds
};

// Get real-time election data
const getElectionRealTimeData = async (electionId) => {
  try {
    const election = await Election.findById(electionId);
    if (!election) return null;

    // Get current vote counts
    const totalVotes = await Vote.countDocuments({ 
      electionId, 
      isValid: true 
    });

    // Get candidate results
    const candidateResults = await Vote.aggregate([
      { $match: { electionId: election._id, isValid: true } },
      {
        $group: {
          _id: '$candidateId',
          candidateName: { $first: '$candidateName' },
          party: { $first: '$party' },
          votes: { $sum: 1 }
        }
      },
      { $sort: { votes: -1 } }
    ]);

    // Get votes in last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const votesLastHour = await Vote.countDocuments({
      electionId,
      isValid: true,
      castedAt: { $gte: oneHourAgo }
    });

    // Get suspicious activity count
    const suspiciousVotes = await Vote.countDocuments({
      electionId,
      'security.isSuspicious': true
    });

    return {
      electionId,
      electionTitle: election.title,
      totalVotes,
      votesLastHour,
      suspiciousVotes,
      candidates: candidateResults.map(candidate => ({
        ...candidate,
        percentage: totalVotes > 0 ? ((candidate.votes / totalVotes) * 100).toFixed(2) : 0
      })),
      lastUpdated: new Date()
    };
  } catch (error) {
    logger.error('Error getting real-time election data:', error);
    return null;
  }
};

// Broadcast election status change
const broadcastElectionStatusChange = (electionId, status, title) => {
  const io = require('../server').io;
  if (io) {
    io.to(`election-${electionId}`).emit('election-status-change', {
      electionId,
      status,
      title,
      timestamp: new Date()
    });
  }
};

// Broadcast security alert
const broadcastSecurityAlert = (electionId, alertData) => {
  const io = require('../server').io;
  if (io) {
    io.to(`election-${electionId}`).emit('security-alert', {
      ...alertData,
      timestamp: new Date()
    });
  }
};

module.exports = {
  handleConnection,
  broadcastElectionStatusChange,
  broadcastSecurityAlert,
  getElectionRealTimeData
};
