const mongoose = require('mongoose');
const crypto = require('crypto');

const voteSchema = new mongoose.Schema({
  // Election Reference
  electionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Election',
    required: [true, 'Election ID is required']
  },
  
  // Vote Data (Anonymized)
  candidateId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Candidate ID is required']
  },
  candidateName: {
    type: String,
    required: [true, 'Candidate name is required']
  },
  party: {
    name: String,
    logo: String
  },
  
  // Geographic Information
  geographic: {
    state: { type: String, required: true },
    district: { type: String, required: true },
    constituency: { type: String, required: true },
    city: String,
    village: String,
    pincode: String
  },
  
  // Voting Session Information
  session: {
    sessionId: {
      type: String,
      required: true,
      unique: true,
      default: () => crypto.randomUUID()
    },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    duration: { type: Number }, // in seconds
    ipAddress: {
      type: String,
      required: true,
      select: false // Don't include in queries by default
    },
    userAgent: {
      type: String,
      select: false
    },
    deviceFingerprint: {
      type: String,
      select: false
    }
  },
  
  // Verification Status
  verification: {
    faceVerification: {
      status: { type: Boolean, required: true },
      confidence: { type: Number, min: 0, max: 1 },
      attempts: { type: Number, default: 1 },
      timestamp: { type: Date, default: Date.now }
    },
    voiceVerification: {
      status: { type: Boolean, required: true },
      multipleVoicesDetected: { type: Boolean, default: false },
      voiceConfidence: { type: Number, min: 0, max: 1 },
      timestamp: { type: Date, default: Date.now }
    },
    documentVerification: {
      status: { type: Boolean, required: true },
      documentsChecked: [String],
      timestamp: { type: Date, default: Date.now }
    },
    livenessCheck: {
      status: { type: Boolean, required: true },
      confidence: { type: Number, min: 0, max: 1 },
      timestamp: { type: Date, default: Date.now }
    }
  },
  
  // Security Flags
  security: {
    isSuspicious: { type: Boolean, default: false },
    suspiciousReasons: [String],
    riskScore: { type: Number, min: 0, max: 100, default: 0 },
    flaggedForReview: { type: Boolean, default: false },
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reviewedAt: Date,
    reviewNotes: String
  },
  
  // Anonymization
  voterHash: {
    type: String,
    required: true,
    select: false // Never include in queries
  },
  
  // Vote Validity
  isValid: { type: Boolean, default: true },
  invalidReason: String,
  
  // Audit Trail
  auditLog: [{
    action: {
      type: String,
      enum: ['vote_cast', 'verification_completed', 'flagged', 'reviewed', 'invalidated'],
      required: true
    },
    timestamp: { type: Date, default: Date.now },
    details: String,
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  
  // Timestamps
  castedAt: { type: Date, default: Date.now },
  processedAt: Date,
  
  // Metadata
  version: { type: Number, default: 1 },
  systemVersion: String
});

// Indexes for performance and analytics
voteSchema.index({ electionId: 1, castedAt: 1 });
voteSchema.index({ candidateId: 1 });
voteSchema.index({ 'geographic.state': 1, 'geographic.district': 1 });
voteSchema.index({ 'geographic.constituency': 1 });
voteSchema.index({ castedAt: 1 });
voteSchema.index({ 'security.isSuspicious': 1 });
voteSchema.index({ 'security.flaggedForReview': 1 });
voteSchema.index({ isValid: 1 });
voteSchema.index({ voterHash: 1 }, { unique: true });

// Compound indexes for analytics
voteSchema.index({ electionId: 1, 'geographic.state': 1, candidateId: 1 });
voteSchema.index({ electionId: 1, 'geographic.district': 1, candidateId: 1 });
voteSchema.index({ electionId: 1, 'geographic.constituency': 1, candidateId: 1 });

// Virtual properties
voteSchema.virtual('votingDuration').get(function() {
  if (this.session.startTime && this.session.endTime) {
    return Math.round((this.session.endTime - this.session.startTime) / 1000);
  }
  return null;
});

voteSchema.virtual('overallVerificationStatus').get(function() {
  return this.verification.faceVerification.status &&
         this.verification.voiceVerification.status &&
         this.verification.documentVerification.status &&
         this.verification.livenessCheck.status;
});

voteSchema.virtual('verificationScore').get(function() {
  const scores = [
    this.verification.faceVerification.confidence || 0,
    this.verification.voiceVerification.voiceConfidence || 0,
    this.verification.livenessCheck.confidence || 0
  ];
  
  return scores.reduce((sum, score) => sum + score, 0) / scores.length;
});

// Pre-save middleware
voteSchema.pre('save', function(next) {
  // Calculate session duration
  if (this.session.startTime && this.session.endTime) {
    this.session.duration = Math.round(
      (this.session.endTime - this.session.startTime) / 1000
    );
  }
  
  // Calculate risk score based on verification results
  this.calculateRiskScore();
  
  // Add audit log entry for new votes
  if (this.isNew) {
    this.auditLog.push({
      action: 'vote_cast',
      timestamp: new Date(),
      details: `Vote cast for candidate ${this.candidateName}`
    });
  }
  
  next();
});

// Instance methods
voteSchema.methods.calculateRiskScore = function() {
  let riskScore = 0;
  
  // Face verification risk
  if (!this.verification.faceVerification.status) {
    riskScore += 30;
  } else if (this.verification.faceVerification.confidence < 0.7) {
    riskScore += 15;
  }
  
  // Voice verification risk
  if (!this.verification.voiceVerification.status) {
    riskScore += 25;
  } else if (this.verification.voiceVerification.multipleVoicesDetected) {
    riskScore += 40;
  }
  
  // Document verification risk
  if (!this.verification.documentVerification.status) {
    riskScore += 20;
  }
  
  // Liveness check risk
  if (!this.verification.livenessCheck.status) {
    riskScore += 25;
  } else if (this.verification.livenessCheck.confidence < 0.8) {
    riskScore += 10;
  }
  
  // Multiple verification attempts
  if (this.verification.faceVerification.attempts > 3) {
    riskScore += 15;
  }
  
  // Unusual voting duration
  if (this.session.duration < 30 || this.session.duration > 1800) { // Less than 30s or more than 30min
    riskScore += 10;
  }
  
  this.security.riskScore = Math.min(riskScore, 100);
  
  // Flag for review if risk score is high
  if (this.security.riskScore >= 50) {
    this.security.isSuspicious = true;
    this.security.flaggedForReview = true;
    
    // Add suspicious reasons
    const reasons = [];
    if (!this.verification.faceVerification.status) reasons.push('Face verification failed');
    if (!this.verification.voiceVerification.status) reasons.push('Voice verification failed');
    if (this.verification.voiceVerification.multipleVoicesDetected) reasons.push('Multiple voices detected');
    if (!this.verification.documentVerification.status) reasons.push('Document verification failed');
    if (!this.verification.livenessCheck.status) reasons.push('Liveness check failed');
    if (this.verification.faceVerification.attempts > 3) reasons.push('Multiple face verification attempts');
    if (this.session.duration < 30) reasons.push('Unusually fast voting');
    if (this.session.duration > 1800) reasons.push('Unusually long voting session');
    
    this.security.suspiciousReasons = reasons;
  }
};

voteSchema.methods.addAuditEntry = function(action, details, performedBy) {
  this.auditLog.push({
    action,
    timestamp: new Date(),
    details,
    performedBy
  });
  
  return this.save();
};

voteSchema.methods.markAsReviewed = function(reviewedBy, notes) {
  this.security.reviewedBy = reviewedBy;
  this.security.reviewedAt = new Date();
  this.security.reviewNotes = notes;
  this.security.flaggedForReview = false;
  
  return this.addAuditEntry('reviewed', notes, reviewedBy);
};

voteSchema.methods.invalidate = function(reason, performedBy) {
  this.isValid = false;
  this.invalidReason = reason;
  
  return this.addAuditEntry('invalidated', reason, performedBy);
};

// Static methods
voteSchema.statics.getElectionResults = async function(electionId) {
  const results = await this.aggregate([
    { $match: { electionId: mongoose.Types.ObjectId(electionId), isValid: true } },
    {
      $group: {
        _id: '$candidateId',
        candidateName: { $first: '$candidateName' },
        party: { $first: '$party' },
        votes: { $sum: 1 }
      }
    },
    { $sort: { votes: -1 } }
  ]);
  
  const totalVotes = results.reduce((sum, candidate) => sum + candidate.votes, 0);
  
  return results.map(candidate => ({
    ...candidate,
    percentage: totalVotes > 0 ? (candidate.votes / totalVotes) * 100 : 0
  }));
};

voteSchema.statics.getGeographicResults = async function(electionId, level = 'state') {
  const groupField = `$geographic.${level}`;
  
  return await this.aggregate([
    { $match: { electionId: mongoose.Types.ObjectId(electionId), isValid: true } },
    {
      $group: {
        _id: {
          location: groupField,
          candidateId: '$candidateId'
        },
        candidateName: { $first: '$candidateName' },
        party: { $first: '$party' },
        votes: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.location',
        totalVotes: { $sum: '$votes' },
        candidates: {
          $push: {
            candidateId: '$_id.candidateId',
            candidateName: '$candidateName',
            party: '$party',
            votes: '$votes'
          }
        }
      }
    },
    {
      $addFields: {
        candidates: {
          $map: {
            input: '$candidates',
            as: 'candidate',
            in: {
              candidateId: '$$candidate.candidateId',
              candidateName: '$$candidate.candidateName',
              party: '$$candidate.party',
              votes: '$$candidate.votes',
              percentage: {
                $multiply: [
                  { $divide: ['$$candidate.votes', '$totalVotes'] },
                  100
                ]
              }
            }
          }
        }
      }
    },
    { $sort: { '_id': 1 } }
  ]);
};

voteSchema.statics.getVotingPatterns = async function(electionId) {
  return await this.aggregate([
    { $match: { electionId: mongoose.Types.ObjectId(electionId), isValid: true } },
    {
      $group: {
        _id: {
          hour: { $hour: '$castedAt' },
          date: { $dateToString: { format: '%Y-%m-%d', date: '$castedAt' } }
        },
        count: { $sum: 1 }
      }
    },
    { $sort: { '_id.date': 1, '_id.hour': 1 } }
  ]);
};

module.exports = mongoose.model('Vote', voteSchema);
