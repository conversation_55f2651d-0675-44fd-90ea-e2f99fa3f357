const twilio = require('twilio');
const logger = require('./logger');

// Initialize Twilio client
let twilioClient = null;

const initializeTwilio = () => {
  if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    return true;
  }
  return false;
};

// Send OTP via SMS
const sendOTP = async (phoneNumber, otp) => {
  try {
    // For development, just log the OTP
    if (process.env.NODE_ENV === 'development') {
      logger.info(`SMS OTP for ${phoneNumber}: ${otp}`);
      return { success: true, messageId: 'dev-message-id' };
    }

    // Initialize Twilio if not already done
    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    const message = `Your AI Voting System verification code is: ${otp}. This code will expire in 10 minutes. Do not share this code with anyone.`;

    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: `+91${phoneNumber}` // Assuming Indian phone numbers
    });

    logger.info(`SMS OTP sent to ${phoneNumber}, Message SID: ${result.sid}`);
    
    return { 
      success: true, 
      messageId: result.sid,
      status: result.status 
    };
  } catch (error) {
    logger.error('Failed to send SMS OTP:', error);
    throw new Error('Failed to send SMS OTP');
  }
};

// Send election notification via SMS
const sendElectionNotification = async (phoneNumber, name, electionTitle, startDate) => {
  try {
    // For development, just log the notification
    if (process.env.NODE_ENV === 'development') {
      logger.info(`SMS Election Notification for ${phoneNumber}: ${electionTitle} starts on ${startDate}`);
      return { success: true, messageId: 'dev-message-id' };
    }

    // Initialize Twilio if not already done
    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    const message = `Hello ${name}, New election "${electionTitle}" is now available. Voting starts on ${new Date(startDate).toLocaleDateString()}. Visit our platform to cast your vote. - AI Voting System`;

    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: `+91${phoneNumber}` // Assuming Indian phone numbers
    });

    logger.info(`Election notification SMS sent to ${phoneNumber}, Message SID: ${result.sid}`);
    
    return { 
      success: true, 
      messageId: result.sid,
      status: result.status 
    };
  } catch (error) {
    logger.error('Failed to send election notification SMS:', error);
    throw new Error('Failed to send election notification SMS');
  }
};

// Send voting reminder via SMS
const sendVotingReminder = async (phoneNumber, name, electionTitle, hoursLeft) => {
  try {
    // For development, just log the reminder
    if (process.env.NODE_ENV === 'development') {
      logger.info(`SMS Voting Reminder for ${phoneNumber}: ${electionTitle} ends in ${hoursLeft} hours`);
      return { success: true, messageId: 'dev-message-id' };
    }

    // Initialize Twilio if not already done
    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    const message = `Hello ${name}, Reminder: "${electionTitle}" voting ends in ${hoursLeft} hours. Don't miss your chance to vote! Visit our platform now. - AI Voting System`;

    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: `+91${phoneNumber}` // Assuming Indian phone numbers
    });

    logger.info(`Voting reminder SMS sent to ${phoneNumber}, Message SID: ${result.sid}`);
    
    return { 
      success: true, 
      messageId: result.sid,
      status: result.status 
    };
  } catch (error) {
    logger.error('Failed to send voting reminder SMS:', error);
    throw new Error('Failed to send voting reminder SMS');
  }
};

// Send security alert via SMS
const sendSecurityAlert = async (phoneNumber, name, alertType, details) => {
  try {
    // For development, just log the alert
    if (process.env.NODE_ENV === 'development') {
      logger.info(`SMS Security Alert for ${phoneNumber}: ${alertType} - ${details}`);
      return { success: true, messageId: 'dev-message-id' };
    }

    // Initialize Twilio if not already done
    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    let message;
    switch (alertType) {
      case 'login_from_new_device':
        message = `Hello ${name}, Your AI Voting System account was accessed from a new device. If this wasn't you, please secure your account immediately.`;
        break;
      case 'password_changed':
        message = `Hello ${name}, Your AI Voting System password was changed. If you didn't make this change, please contact support immediately.`;
        break;
      case 'suspicious_activity':
        message = `Hello ${name}, Suspicious activity detected on your AI Voting System account. Please review your account security.`;
        break;
      default:
        message = `Hello ${name}, Security alert for your AI Voting System account: ${details}`;
    }

    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: `+91${phoneNumber}` // Assuming Indian phone numbers
    });

    logger.info(`Security alert SMS sent to ${phoneNumber}, Message SID: ${result.sid}`);
    
    return { 
      success: true, 
      messageId: result.sid,
      status: result.status 
    };
  } catch (error) {
    logger.error('Failed to send security alert SMS:', error);
    throw new Error('Failed to send security alert SMS');
  }
};

// Verify phone number format
const isValidPhoneNumber = (phoneNumber) => {
  // Indian phone number validation (10 digits)
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phoneNumber);
};

// Get SMS delivery status
const getMessageStatus = async (messageSid) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      return { status: 'delivered', dateUpdated: new Date() };
    }

    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    const message = await twilioClient.messages(messageSid).fetch();
    
    return {
      status: message.status,
      errorCode: message.errorCode,
      errorMessage: message.errorMessage,
      dateCreated: message.dateCreated,
      dateUpdated: message.dateUpdated,
      dateSent: message.dateSent
    };
  } catch (error) {
    logger.error('Failed to get message status:', error);
    throw new Error('Failed to get message status');
  }
};

// Bulk SMS sending (for election notifications)
const sendBulkSMS = async (recipients, message) => {
  try {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await sendCustomMessage(recipient.phoneNumber, message);
        results.push({
          phoneNumber: recipient.phoneNumber,
          success: true,
          messageId: result.messageId
        });
      } catch (error) {
        results.push({
          phoneNumber: recipient.phoneNumber,
          success: false,
          error: error.message
        });
      }
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    logger.info(`Bulk SMS completed: ${successCount} successful, ${failureCount} failed`);
    
    return {
      success: true,
      totalSent: successCount,
      totalFailed: failureCount,
      results
    };
  } catch (error) {
    logger.error('Failed to send bulk SMS:', error);
    throw new Error('Failed to send bulk SMS');
  }
};

// Send custom message
const sendCustomMessage = async (phoneNumber, message) => {
  try {
    // For development, just log the message
    if (process.env.NODE_ENV === 'development') {
      logger.info(`SMS for ${phoneNumber}: ${message}`);
      return { success: true, messageId: 'dev-message-id' };
    }

    // Initialize Twilio if not already done
    if (!twilioClient && !initializeTwilio()) {
      throw new Error('Twilio credentials not configured');
    }

    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: `+91${phoneNumber}` // Assuming Indian phone numbers
    });

    logger.info(`Custom SMS sent to ${phoneNumber}, Message SID: ${result.sid}`);
    
    return { 
      success: true, 
      messageId: result.sid,
      status: result.status 
    };
  } catch (error) {
    logger.error('Failed to send custom SMS:', error);
    throw new Error('Failed to send custom SMS');
  }
};

module.exports = {
  sendOTP,
  sendElectionNotification,
  sendVotingReminder,
  sendSecurityAlert,
  sendBulkSMS,
  sendCustomMessage,
  getMessageStatus,
  isValidPhoneNumber
};
