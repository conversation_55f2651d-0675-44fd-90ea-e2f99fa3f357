require('dotenv').config();

console.log('Testing imports...');

try {
  console.log('1. Testing logger...');
  const logger = require('./utils/logger');
  console.log('✓ Logger imported successfully');

  console.log('2. Testing User model...');
  const User = require('./models/User');
  console.log('✓ User model imported successfully');

  console.log('3. Testing auth middleware...');
  const { protect } = require('./middleware/auth');
  console.log('✓ Auth middleware imported successfully');

  console.log('4. Testing error handler...');
  const { globalErrorHandler } = require('./middleware/errorHandler');
  console.log('✓ Error handler imported successfully');

  console.log('5. Testing socket handler...');
  const { handleConnection } = require('./socket/socketHandler');
  console.log('✓ Socket handler imported successfully');

  console.log('All imports successful!');
} catch (error) {
  console.error('Import error:', error.message);
  console.error('Stack:', error.stack);
}
