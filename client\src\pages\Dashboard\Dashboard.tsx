import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  HowToVote,
  Person,
  Security,
  CheckCircle,
  Warning,
  Schedule,
  TrendingUp,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import LoadingSpinner from '../../components/Common/LoadingSpinner';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { connected } = useSocket();
  const navigate = useNavigate();

  if (!user) {
    return <LoadingSpinner message="Loading user data..." />;
  }

  // Calculate verification progress
  const verificationSteps = [
    { key: 'email', label: 'Email Verification', completed: user.verificationStatus.email },
    { key: 'phone', label: 'Phone Verification', completed: user.verificationStatus.phone },
    { key: 'document', label: 'Document Verification', completed: user.verificationStatus.document },
    { key: 'face', label: 'Face Verification', completed: user.verificationStatus.face },
  ];

  const completedSteps = verificationSteps.filter(step => step.completed).length;
  const verificationProgress = (completedSteps / verificationSteps.length) * 100;

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome back, {user.name}!
      </Typography>

      <Grid container spacing={3}>
        {/* User Status Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Person sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Account Status</Typography>
              </Box>
              
              <Box mb={2}>
                <Chip
                  label={user.isEligible ? 'Eligible to Vote' : 'Verification Required'}
                  color={user.isEligible ? 'success' : 'warning'}
                  icon={user.isEligible ? <CheckCircle /> : <Warning />}
                />
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Verification Progress: {completedSteps}/{verificationSteps.length} completed
              </Typography>
              
              <LinearProgress
                variant="determinate"
                value={verificationProgress}
                sx={{ mb: 2, height: 8, borderRadius: 4 }}
              />

              <List dense>
                {verificationSteps.map((step) => (
                  <ListItem key={step.key} sx={{ px: 0 }}>
                    <ListItemIcon>
                      {step.completed ? (
                        <CheckCircle color="success" />
                      ) : (
                        <Schedule color="action" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={step.label}
                      secondary={step.completed ? 'Completed' : 'Pending'}
                    />
                  </ListItem>
                ))}
              </List>

              {!user.isEligible && (
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => navigate('/profile')}
                  sx={{ mt: 2 }}
                >
                  Complete Verification
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <HowToVote sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Quick Actions</Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<HowToVote />}
                    onClick={() => navigate('/elections')}
                    disabled={!user.isEligible}
                  >
                    View Available Elections
                  </Button>
                </Grid>
                
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<Person />}
                    onClick={() => navigate('/profile')}
                  >
                    Update Profile
                  </Button>
                </Grid>

                {['admin', 'election_officer', 'super_admin'].includes(user.role) && (
                  <Grid item xs={12}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<TrendingUp />}
                      onClick={() => navigate('/admin')}
                    >
                      Admin Dashboard
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Security sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">System Status</Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Chip
                      label={connected ? 'Connected' : 'Disconnected'}
                      color={connected ? 'success' : 'error'}
                      variant="outlined"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Real-time Updates
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Chip
                      label="Secure"
                      color="success"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Connection Status
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Chip
                      label={user.role.replace('_', ' ').toUpperCase()}
                      color="primary"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      User Role
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Chip
                      label={user.address.constituency}
                      color="info"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Constituency
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Alerts and Notifications */}
        {!user.isEligible && (
          <Grid item xs={12}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Action Required:</strong> Complete your verification process to become eligible for voting.
                You need to verify your email, phone, upload documents, and complete face verification.
              </Typography>
            </Alert>
          </Grid>
        )}

        {user.isEligible && (
          <Grid item xs={12}>
            <Alert severity="success">
              <Typography variant="body2">
                <strong>Ready to Vote:</strong> Your account is fully verified and you're eligible to participate in elections.
                Check the Elections page for available voting opportunities.
              </Typography>
            </Alert>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Dashboard;
