const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const logger = require('./logger');

class DocumentVerificationService {
  constructor() {
    this.isInitialized = false;
    this.supportedFormats = ['jpg', 'jpeg', 'png', 'pdf'];
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
  }

  // Initialize the document verification service
  async initialize() {
    try {
      this.isInitialized = true;
      logger.info('Document verification service initialized');
    } catch (error) {
      logger.error('Failed to initialize document verification service:', error);
      throw new Error('Document verification initialization failed');
    }
  }

  // Verify document format and basic properties
  async verifyDocumentFormat(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error('Document file not found');
      }

      const stats = fs.statSync(filePath);
      const fileExtension = path.extname(filePath).toLowerCase().substring(1);
      
      // Check file size
      if (stats.size > this.maxFileSize) {
        return {
          success: false,
          error: 'File size exceeds maximum limit (10MB)',
          fileSize: stats.size
        };
      }

      // Check file format
      if (!this.supportedFormats.includes(fileExtension)) {
        return {
          success: false,
          error: 'Unsupported file format',
          supportedFormats: this.supportedFormats
        };
      }

      // Additional checks for images
      if (['jpg', 'jpeg', 'png'].includes(fileExtension)) {
        const imageInfo = await this.getImageInfo(filePath);
        return {
          success: true,
          fileType: 'image',
          format: fileExtension,
          fileSize: stats.size,
          imageInfo: imageInfo
        };
      }

      // For PDF files
      if (fileExtension === 'pdf') {
        const pdfInfo = await this.getPDFInfo(filePath);
        return {
          success: true,
          fileType: 'pdf',
          format: fileExtension,
          fileSize: stats.size,
          pdfInfo: pdfInfo
        };
      }

      return {
        success: true,
        fileType: 'unknown',
        format: fileExtension,
        fileSize: stats.size
      };
    } catch (error) {
      logger.error('Document format verification failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get image information
  async getImageInfo(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      
      return {
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        orientation: metadata.orientation,
        quality: this.assessImageQuality(metadata)
      };
    } catch (error) {
      logger.error('Failed to get image info:', error);
      return {
        width: null,
        height: null,
        quality: 'unknown'
      };
    }
  }

  // Get PDF information
  async getPDFInfo(pdfPath) {
    try {
      // Basic PDF info - in a real implementation, you'd use a PDF library
      const stats = fs.statSync(pdfPath);
      
      return {
        pages: 1, // Assume single page for ID documents
        size: stats.size,
        quality: 'unknown'
      };
    } catch (error) {
      logger.error('Failed to get PDF info:', error);
      return {
        pages: null,
        size: null,
        quality: 'unknown'
      };
    }
  }

  // Assess image quality
  assessImageQuality(metadata) {
    try {
      const { width, height, density } = metadata;
      
      // Minimum resolution requirements for ID documents
      const minWidth = 800;
      const minHeight = 600;
      const minDensity = 150;
      
      if (width < minWidth || height < minHeight) {
        return 'low_resolution';
      }
      
      if (density && density < minDensity) {
        return 'low_density';
      }
      
      return 'good';
    } catch (error) {
      return 'unknown';
    }
  }

  // Verify Aadhaar card
  async verifyAadhaarCard(filePath, userDetails) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Basic format verification
      const formatCheck = await this.verifyDocumentFormat(filePath);
      if (!formatCheck.success) {
        return formatCheck;
      }

      // Aadhaar-specific checks
      const aadhaarChecks = {
        formatValid: true,
        qualityCheck: this.checkDocumentQuality(formatCheck),
        textDetection: await this.detectText(filePath),
        aadhaarPattern: await this.detectAadhaarPattern(filePath),
        faceDetection: await this.detectFaceInDocument(filePath)
      };

      // Validate Aadhaar number format
      const aadhaarNumberValid = this.validateAadhaarNumber(userDetails.aadhaarNumber);
      
      // Calculate overall verification score
      const score = this.calculateVerificationScore(aadhaarChecks, aadhaarNumberValid);
      
      return {
        success: true,
        documentType: 'aadhaar',
        verified: score >= 0.7,
        confidence: score,
        checks: aadhaarChecks,
        aadhaarNumberValid: aadhaarNumberValid,
        details: formatCheck
      };
    } catch (error) {
      logger.error('Aadhaar verification failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Verify Voter ID card
  async verifyVoterIdCard(filePath, userDetails) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Basic format verification
      const formatCheck = await this.verifyDocumentFormat(filePath);
      if (!formatCheck.success) {
        return formatCheck;
      }

      // Voter ID-specific checks
      const voterIdChecks = {
        formatValid: true,
        qualityCheck: this.checkDocumentQuality(formatCheck),
        textDetection: await this.detectText(filePath),
        voterIdPattern: await this.detectVoterIdPattern(filePath),
        faceDetection: await this.detectFaceInDocument(filePath)
      };

      // Validate Voter ID format
      const voterIdValid = this.validateVoterIdFormat(userDetails.voterIdNumber);
      
      // Calculate overall verification score
      const score = this.calculateVerificationScore(voterIdChecks, voterIdValid);
      
      return {
        success: true,
        documentType: 'voter_id',
        verified: score >= 0.7,
        confidence: score,
        checks: voterIdChecks,
        voterIdValid: voterIdValid,
        details: formatCheck
      };
    } catch (error) {
      logger.error('Voter ID verification failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Check document quality
  checkDocumentQuality(formatCheck) {
    try {
      if (formatCheck.fileType === 'image' && formatCheck.imageInfo) {
        const { width, height, quality } = formatCheck.imageInfo;
        
        return {
          resolution: width >= 800 && height >= 600 ? 'good' : 'low',
          quality: quality,
          score: quality === 'good' ? 1.0 : quality === 'low_resolution' ? 0.5 : 0.7
        };
      }
      
      return {
        resolution: 'unknown',
        quality: 'unknown',
        score: 0.5
      };
    } catch (error) {
      return {
        resolution: 'error',
        quality: 'error',
        score: 0
      };
    }
  }

  // Detect text in document (placeholder)
  async detectText(filePath) {
    try {
      // In a real implementation, you would use OCR libraries like Tesseract
      // For now, return a mock result
      return {
        success: true,
        textDetected: true,
        confidence: 0.8,
        method: 'mock_ocr'
      };
    } catch (error) {
      return {
        success: false,
        textDetected: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  // Detect Aadhaar pattern
  async detectAadhaarPattern(filePath) {
    try {
      // Mock Aadhaar pattern detection
      // In reality, you'd look for specific visual elements of Aadhaar cards
      return {
        patternDetected: true,
        confidence: 0.7,
        elements: ['logo', 'qr_code', 'number_format'],
        method: 'mock_pattern_detection'
      };
    } catch (error) {
      return {
        patternDetected: false,
        confidence: 0,
        elements: [],
        error: error.message
      };
    }
  }

  // Detect Voter ID pattern
  async detectVoterIdPattern(filePath) {
    try {
      // Mock Voter ID pattern detection
      return {
        patternDetected: true,
        confidence: 0.7,
        elements: ['logo', 'hologram', 'number_format'],
        method: 'mock_pattern_detection'
      };
    } catch (error) {
      return {
        patternDetected: false,
        confidence: 0,
        elements: [],
        error: error.message
      };
    }
  }

  // Detect face in document
  async detectFaceInDocument(filePath) {
    try {
      // Use the face recognition service to detect faces in the document
      const faceRecognition = require('./faceRecognition');
      const faceDetection = await faceRecognition.detectFaces(filePath);
      
      return {
        faceDetected: faceDetection.success && faceDetection.faceCount > 0,
        faceCount: faceDetection.faceCount,
        confidence: faceDetection.success ? 0.8 : 0,
        faces: faceDetection.faces || []
      };
    } catch (error) {
      return {
        faceDetected: false,
        faceCount: 0,
        confidence: 0,
        error: error.message
      };
    }
  }

  // Validate Aadhaar number format
  validateAadhaarNumber(aadhaarNumber) {
    try {
      // Aadhaar number should be 12 digits
      const aadhaarRegex = /^[0-9]{12}$/;
      
      if (!aadhaarRegex.test(aadhaarNumber)) {
        return {
          valid: false,
          error: 'Invalid Aadhaar number format'
        };
      }

      // Additional validation: Aadhaar numbers don't start with 0 or 1
      if (aadhaarNumber.startsWith('0') || aadhaarNumber.startsWith('1')) {
        return {
          valid: false,
          error: 'Aadhaar number cannot start with 0 or 1'
        };
      }

      return {
        valid: true,
        formatted: aadhaarNumber.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3')
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // Validate Voter ID format
  validateVoterIdFormat(voterIdNumber) {
    try {
      // Voter ID format: 3 letters followed by 7 digits
      const voterIdRegex = /^[A-Z]{3}[0-9]{7}$/;
      
      if (!voterIdRegex.test(voterIdNumber)) {
        return {
          valid: false,
          error: 'Invalid Voter ID format (should be **********)'
        };
      }

      return {
        valid: true,
        formatted: voterIdNumber
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // Calculate overall verification score
  calculateVerificationScore(checks, numberValid) {
    try {
      let score = 0;
      let totalChecks = 0;

      // Quality check (20%)
      if (checks.qualityCheck) {
        score += checks.qualityCheck.score * 0.2;
        totalChecks += 0.2;
      }

      // Text detection (20%)
      if (checks.textDetection && checks.textDetection.success) {
        score += checks.textDetection.confidence * 0.2;
        totalChecks += 0.2;
      }

      // Pattern detection (30%)
      const patternCheck = checks.aadhaarPattern || checks.voterIdPattern;
      if (patternCheck && patternCheck.patternDetected) {
        score += patternCheck.confidence * 0.3;
        totalChecks += 0.3;
      }

      // Face detection (20%)
      if (checks.faceDetection && checks.faceDetection.faceDetected) {
        score += checks.faceDetection.confidence * 0.2;
        totalChecks += 0.2;
      }

      // Number validation (10%)
      if (numberValid && numberValid.valid) {
        score += 0.1;
        totalChecks += 0.1;
      }

      // Normalize score
      return totalChecks > 0 ? score / totalChecks : 0;
    } catch (error) {
      logger.error('Score calculation failed:', error);
      return 0;
    }
  }

  // Extract face from document for comparison
  async extractFaceFromDocument(filePath) {
    try {
      const faceDetection = await this.detectFaceInDocument(filePath);
      
      if (!faceDetection.faceDetected || faceDetection.faceCount === 0) {
        return {
          success: false,
          error: 'No face detected in document'
        };
      }

      if (faceDetection.faceCount > 1) {
        return {
          success: false,
          error: 'Multiple faces detected in document'
        };
      }

      // Extract face embedding using face recognition service
      const faceRecognition = require('./faceRecognition');
      const face = faceDetection.faces[0];
      const embeddingResult = await faceRecognition.extractFaceEmbedding(filePath, face);
      
      return {
        success: embeddingResult.success,
        embedding: embeddingResult.embedding,
        face: face,
        method: embeddingResult.method
      };
    } catch (error) {
      logger.error('Face extraction from document failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Compare document face with live photo
  async compareFaces(documentPath, livePath) {
    try {
      // Extract face from document
      const documentFace = await this.extractFaceFromDocument(documentPath);
      if (!documentFace.success) {
        return documentFace;
      }

      // Extract face from live photo
      const faceRecognition = require('./faceRecognition');
      const liveFaceDetection = await faceRecognition.detectFaces(livePath);
      
      if (!liveFaceDetection.success || liveFaceDetection.faceCount === 0) {
        return {
          success: false,
          error: 'No face detected in live photo'
        };
      }

      if (liveFaceDetection.faceCount > 1) {
        return {
          success: false,
          error: 'Multiple faces detected in live photo'
        };
      }

      const liveFace = liveFaceDetection.faces[0];
      const liveEmbedding = await faceRecognition.extractFaceEmbedding(livePath, liveFace);
      
      if (!liveEmbedding.success) {
        return {
          success: false,
          error: 'Failed to extract face embedding from live photo'
        };
      }

      // Compare embeddings
      const comparison = faceRecognition.compareFaces(documentFace.embedding, liveEmbedding.embedding);
      
      return {
        success: true,
        isMatch: comparison.isMatch,
        confidence: comparison.similarity,
        threshold: comparison.threshold,
        details: {
          documentFace: documentFace,
          liveFace: liveEmbedding,
          comparison: comparison
        }
      };
    } catch (error) {
      logger.error('Face comparison failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
const documentVerificationService = new DocumentVerificationService();
module.exports = documentVerificationService;
