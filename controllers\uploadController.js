const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const documentVerification = require('../utils/documentVerification');
const faceRecognition = require('../utils/faceRecognition');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads', req.user._id.toString());
    
    // Create user-specific directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    const fieldName = file.fieldname;
    
    cb(null, `${fieldName}-${uniqueSuffix}${extension}`);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = {
    'aadhaarCard': ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
    'voterIdCard': ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
    'profilePhoto': ['image/jpeg', 'image/jpg', 'image/png']
  };

  const fieldAllowedTypes = allowedTypes[file.fieldname];
  
  if (fieldAllowedTypes && fieldAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError(`Invalid file type for ${file.fieldname}. Allowed types: ${fieldAllowedTypes?.join(', ')}`, 400), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 3 // Maximum 3 files at once
  }
});

// Upload middleware
const uploadMiddleware = upload.fields([
  { name: 'aadhaarCard', maxCount: 1 },
  { name: 'voterIdCard', maxCount: 1 },
  { name: 'profilePhoto', maxCount: 1 }
]);

// Upload documents
const uploadDocuments = catchAsync(async (req, res, next) => {
  // Check if files were uploaded
  if (!req.files || Object.keys(req.files).length === 0) {
    return next(new AppError('No files uploaded', 400));
  }

  const user = await User.findById(req.user._id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const uploadResults = {};
  const verificationResults = {};

  try {
    // Process each uploaded file
    for (const [fieldName, files] of Object.entries(req.files)) {
      const file = files[0]; // Get first file (maxCount: 1)
      const filePath = file.path;

      // Store file information
      uploadResults[fieldName] = {
        filename: file.filename,
        originalName: file.originalname,
        path: filePath,
        size: file.size,
        mimetype: file.mimetype,
        uploadedAt: new Date()
      };

      // Verify document based on type
      if (fieldName === 'aadhaarCard') {
        const verification = await documentVerification.verifyAadhaarCard(filePath, {
          aadhaarNumber: user.aadhaarNumber
        });
        verificationResults.aadhaarCard = verification;
        
        // Update user document info
        user.documents.aadhaarCard = {
          ...uploadResults[fieldName],
          verified: verification.verified
        };
      } else if (fieldName === 'voterIdCard') {
        const verification = await documentVerification.verifyVoterIdCard(filePath, {
          voterIdNumber: user.voterIdNumber
        });
        verificationResults.voterIdCard = verification;
        
        // Update user document info
        user.documents.voterIdCard = {
          ...uploadResults[fieldName],
          verified: verification.verified
        };
      } else if (fieldName === 'profilePhoto') {
        // Process profile photo
        const processedPhoto = await processProfilePhoto(filePath);
        uploadResults[fieldName].processedPath = processedPhoto.path;
        
        // Extract face embedding for future verification
        const faceEmbedding = await faceRecognition.extractFaceEmbedding(filePath);
        if (faceEmbedding.success) {
          user.faceEmbedding = faceEmbedding.embedding;
          user.isFaceVerified = true;
        }
        
        // Update user profile photo
        user.documents.profilePhoto = uploadResults[fieldName];
      }
    }

    // Check if all required documents are verified
    const allDocumentsVerified = 
      user.documents.aadhaarCard?.verified && 
      user.documents.voterIdCard?.verified;
    
    if (allDocumentsVerified) {
      user.isDocumentVerified = true;
      
      // Check if user is now eligible (all verifications complete)
      if (user.isEmailVerified && user.isPhoneVerified && user.isDocumentVerified && user.isFaceVerified) {
        user.isEligible = true;
      }
    }

    await user.save();

    // Create audit log
    await AuditLog.createLog({
      eventType: 'document_upload',
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'document_upload',
      description: `Documents uploaded: ${Object.keys(req.files).join(', ')}`,
      success: true,
      metadata: {
        uploadedFiles: Object.keys(req.files),
        verificationResults: verificationResults
      },
      context: {
        source: 'web',
        component: 'upload_controller'
      }
    });

    logger.info(`Documents uploaded for user: ${user.email}, Files: ${Object.keys(req.files).join(', ')}`);

    res.status(200).json({
      success: true,
      message: 'Documents uploaded successfully',
      data: {
        uploadResults: uploadResults,
        verificationResults: verificationResults,
        userStatus: {
          isDocumentVerified: user.isDocumentVerified,
          isFaceVerified: user.isFaceVerified,
          isEligible: user.isEligible
        }
      }
    });
  } catch (error) {
    // Clean up uploaded files on error
    for (const [fieldName, files] of Object.entries(req.files)) {
      const file = files[0];
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
    }
    
    logger.error('Document upload failed:', error);
    return next(new AppError('Document upload failed', 500));
  }
});

// Process profile photo (resize, optimize)
const processProfilePhoto = async (filePath) => {
  try {
    const processedPath = filePath.replace(/\.(jpg|jpeg|png)$/i, '_processed.jpg');
    
    await sharp(filePath)
      .resize(400, 400, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({
        quality: 85,
        progressive: true
      })
      .toFile(processedPath);
    
    return {
      success: true,
      path: processedPath
    };
  } catch (error) {
    logger.error('Profile photo processing failed:', error);
    return {
      success: false,
      path: filePath,
      error: error.message
    };
  }
};

// Verify face against documents
const verifyFaceAgainstDocuments = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if user has uploaded documents
  if (!user.documents.aadhaarCard?.path && !user.documents.voterIdCard?.path) {
    return next(new AppError('No identity documents found. Please upload your documents first.', 400));
  }

  if (!user.documents.profilePhoto?.path) {
    return next(new AppError('No profile photo found. Please upload your photo first.', 400));
  }

  try {
    const verificationResults = {};

    // Compare with Aadhaar card if available
    if (user.documents.aadhaarCard?.path) {
      const aadhaarComparison = await documentVerification.compareFaces(
        user.documents.aadhaarCard.path,
        user.documents.profilePhoto.path
      );
      verificationResults.aadhaarComparison = aadhaarComparison;
    }

    // Compare with Voter ID if available
    if (user.documents.voterIdCard?.path) {
      const voterIdComparison = await documentVerification.compareFaces(
        user.documents.voterIdCard.path,
        user.documents.profilePhoto.path
      );
      verificationResults.voterIdComparison = voterIdComparison;
    }

    // Determine overall verification status
    const aadhaarMatch = verificationResults.aadhaarComparison?.isMatch || false;
    const voterIdMatch = verificationResults.voterIdComparison?.isMatch || false;
    const overallMatch = aadhaarMatch || voterIdMatch;

    if (overallMatch) {
      user.isFaceVerified = true;
      
      // Check if user is now eligible
      if (user.isEmailVerified && user.isPhoneVerified && user.isDocumentVerified && user.isFaceVerified) {
        user.isEligible = true;
      }
      
      await user.save();
    }

    // Create audit log
    await AuditLog.createLog({
      eventType: 'face_verification',
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'face_verification',
      description: 'Face verification against documents',
      success: overallMatch,
      metadata: verificationResults,
      context: {
        source: 'web',
        component: 'upload_controller'
      }
    });

    logger.info(`Face verification for user: ${user.email}, Result: ${overallMatch}`);

    res.status(200).json({
      success: true,
      message: overallMatch ? 'Face verification successful' : 'Face verification failed',
      data: {
        verified: overallMatch,
        verificationResults: verificationResults,
        userStatus: {
          isFaceVerified: user.isFaceVerified,
          isEligible: user.isEligible
        }
      }
    });
  } catch (error) {
    logger.error('Face verification failed:', error);
    return next(new AppError('Face verification failed', 500));
  }
});

// Get user documents
const getUserDocuments = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id).select('documents isDocumentVerified isFaceVerified isEligible');
  
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Remove sensitive file paths for security
  const sanitizedDocuments = {
    aadhaarCard: user.documents.aadhaarCard ? {
      filename: user.documents.aadhaarCard.filename,
      originalName: user.documents.aadhaarCard.originalName,
      size: user.documents.aadhaarCard.size,
      uploadedAt: user.documents.aadhaarCard.uploadedAt,
      verified: user.documents.aadhaarCard.verified
    } : null,
    voterIdCard: user.documents.voterIdCard ? {
      filename: user.documents.voterIdCard.filename,
      originalName: user.documents.voterIdCard.originalName,
      size: user.documents.voterIdCard.size,
      uploadedAt: user.documents.voterIdCard.uploadedAt,
      verified: user.documents.voterIdCard.verified
    } : null,
    profilePhoto: user.documents.profilePhoto ? {
      filename: user.documents.profilePhoto.filename,
      originalName: user.documents.profilePhoto.originalName,
      size: user.documents.profilePhoto.size,
      uploadedAt: user.documents.profilePhoto.uploadedAt
    } : null
  };

  res.status(200).json({
    success: true,
    data: {
      documents: sanitizedDocuments,
      verificationStatus: {
        isDocumentVerified: user.isDocumentVerified,
        isFaceVerified: user.isFaceVerified,
        isEligible: user.isEligible
      }
    }
  });
});

// Delete document
const deleteDocument = catchAsync(async (req, res, next) => {
  const { documentType } = req.params;
  
  if (!['aadhaarCard', 'voterIdCard', 'profilePhoto'].includes(documentType)) {
    return next(new AppError('Invalid document type', 400));
  }

  const user = await User.findById(req.user._id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const document = user.documents[documentType];
  if (!document) {
    return next(new AppError('Document not found', 404));
  }

  try {
    // Delete file from filesystem
    if (fs.existsSync(document.path)) {
      fs.unlinkSync(document.path);
    }
    
    // Delete processed file if exists
    if (document.processedPath && fs.existsSync(document.processedPath)) {
      fs.unlinkSync(document.processedPath);
    }

    // Remove document from user record
    user.documents[documentType] = undefined;
    
    // Update verification status
    if (documentType === 'aadhaarCard' || documentType === 'voterIdCard') {
      user.isDocumentVerified = false;
      user.isEligible = false;
    } else if (documentType === 'profilePhoto') {
      user.isFaceVerified = false;
      user.faceEmbedding = undefined;
      user.isEligible = false;
    }

    await user.save();

    logger.info(`Document deleted for user: ${user.email}, Type: ${documentType}`);

    res.status(200).json({
      success: true,
      message: 'Document deleted successfully',
      data: {
        deletedDocument: documentType,
        verificationStatus: {
          isDocumentVerified: user.isDocumentVerified,
          isFaceVerified: user.isFaceVerified,
          isEligible: user.isEligible
        }
      }
    });
  } catch (error) {
    logger.error('Document deletion failed:', error);
    return next(new AppError('Document deletion failed', 500));
  }
});

module.exports = {
  uploadMiddleware,
  uploadDocuments,
  verifyFaceAgainstDocuments,
  getUserDocuments,
  deleteDocument
};
