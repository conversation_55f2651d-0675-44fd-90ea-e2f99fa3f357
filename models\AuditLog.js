const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema({
  // Event Information
  eventType: {
    type: String,
    enum: [
      'user_registration',
      'user_login',
      'user_logout',
      'password_change',
      'document_upload',
      'document_verification',
      'face_verification',
      'voice_verification',
      'vote_cast',
      'election_created',
      'election_modified',
      'election_started',
      'election_ended',
      'candidate_added',
      'candidate_removed',
      'results_published',
      'suspicious_activity',
      'system_access',
      'data_export',
      'configuration_change',
      'security_incident',
      'backup_created',
      'system_error'
    ],
    required: [true, 'Event type is required']
  },
  
  // Actor Information
  actor: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    userEmail: String,
    userName: String,
    userRole: String,
    ipAddress: { type: String, required: true },
    userAgent: String,
    sessionId: String
  },
  
  // Target Information (what was acted upon)
  target: {
    resourceType: {
      type: String,
      enum: ['user', 'election', 'vote', 'candidate', 'system', 'document', 'configuration']
    },
    resourceId: mongoose.Schema.Types.ObjectId,
    resourceName: String,
    previousState: mongoose.Schema.Types.Mixed,
    newState: mongoose.Schema.Types.Mixed
  },
  
  // Event Details
  details: {
    action: { type: String, required: true },
    description: String,
    metadata: mongoose.Schema.Types.Mixed,
    success: { type: Boolean, required: true },
    errorMessage: String,
    errorCode: String
  },
  
  // Context Information
  context: {
    electionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Election' },
    electionTitle: String,
    requestId: String,
    correlationId: String,
    source: {
      type: String,
      enum: ['web', 'mobile', 'api', 'system', 'admin'],
      default: 'web'
    },
    component: String, // Which part of the system generated this log
    version: String
  },
  
  // Security Information
  security: {
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    },
    threatIndicators: [String],
    requiresReview: { type: Boolean, default: false },
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reviewedAt: Date,
    reviewNotes: String
  },
  
  // Geographic Information
  geographic: {
    country: String,
    state: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    timezone: String
  },
  
  // Compliance and Legal
  compliance: {
    dataClassification: {
      type: String,
      enum: ['public', 'internal', 'confidential', 'restricted'],
      default: 'internal'
    },
    retentionPeriod: { type: Number, default: 2555 }, // days (7 years default)
    legalHold: { type: Boolean, default: false },
    personalDataIncluded: { type: Boolean, default: false }
  },
  
  // Timestamps
  timestamp: { type: Date, default: Date.now, required: true },
  serverTimestamp: { type: Date, default: Date.now },
  
  // System Information
  system: {
    hostname: String,
    processId: Number,
    threadId: String,
    memoryUsage: Number,
    cpuUsage: Number
  },
  
  // Data Integrity
  checksum: String,
  signature: String,
  
  // Indexing and Search
  tags: [String],
  searchableText: String
});

// Indexes for performance
auditLogSchema.index({ timestamp: -1 });
auditLogSchema.index({ eventType: 1, timestamp: -1 });
auditLogSchema.index({ 'actor.userId': 1, timestamp: -1 });
auditLogSchema.index({ 'actor.ipAddress': 1, timestamp: -1 });
auditLogSchema.index({ 'context.electionId': 1, timestamp: -1 });
auditLogSchema.index({ 'security.riskLevel': 1, timestamp: -1 });
auditLogSchema.index({ 'security.requiresReview': 1, timestamp: -1 });
auditLogSchema.index({ 'details.success': 1, timestamp: -1 });
auditLogSchema.index({ tags: 1 });
auditLogSchema.index({ searchableText: 'text' });

// Compound indexes for common queries
auditLogSchema.index({ eventType: 1, 'actor.userId': 1, timestamp: -1 });
auditLogSchema.index({ 'context.electionId': 1, eventType: 1, timestamp: -1 });
auditLogSchema.index({ 'security.riskLevel': 1, 'security.requiresReview': 1, timestamp: -1 });

// Virtual properties
auditLogSchema.virtual('age').get(function() {
  return Date.now() - this.timestamp;
});

auditLogSchema.virtual('isRecent').get(function() {
  const oneHour = 60 * 60 * 1000;
  return (Date.now() - this.timestamp) < oneHour;
});

auditLogSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// Pre-save middleware
auditLogSchema.pre('save', function(next) {
  // Generate searchable text for full-text search
  const searchableFields = [
    this.eventType,
    this.actor.userEmail,
    this.actor.userName,
    this.target.resourceName,
    this.details.action,
    this.details.description,
    this.context.electionTitle
  ].filter(Boolean);
  
  this.searchableText = searchableFields.join(' ').toLowerCase();
  
  // Add system information
  this.system.hostname = require('os').hostname();
  this.system.processId = process.pid;
  
  // Generate checksum for data integrity
  const crypto = require('crypto');
  const dataToHash = JSON.stringify({
    eventType: this.eventType,
    actor: this.actor,
    target: this.target,
    details: this.details,
    timestamp: this.timestamp
  });
  
  this.checksum = crypto.createHash('sha256').update(dataToHash).digest('hex');
  
  // Determine risk level based on event type and context
  this.assessRiskLevel();
  
  next();
});

// Instance methods
auditLogSchema.methods.assessRiskLevel = function() {
  const highRiskEvents = [
    'suspicious_activity',
    'security_incident',
    'unauthorized_access',
    'data_export',
    'configuration_change'
  ];
  
  const mediumRiskEvents = [
    'password_change',
    'document_verification',
    'election_modified',
    'candidate_removed',
    'results_published'
  ];
  
  if (highRiskEvents.includes(this.eventType)) {
    this.security.riskLevel = 'high';
    this.security.requiresReview = true;
  } else if (mediumRiskEvents.includes(this.eventType)) {
    this.security.riskLevel = 'medium';
  } else if (!this.details.success) {
    this.security.riskLevel = 'medium';
  } else {
    this.security.riskLevel = 'low';
  }
  
  // Check for suspicious patterns
  if (this.actor.ipAddress && this.isNewIPForUser()) {
    this.security.riskLevel = 'medium';
    this.security.threatIndicators.push('new_ip_address');
  }
  
  // Check for failed attempts
  if (!this.details.success && this.eventType.includes('login')) {
    this.security.riskLevel = 'medium';
    this.security.threatIndicators.push('failed_login');
  }
};

auditLogSchema.methods.isNewIPForUser = function() {
  // This would typically check against recent logs for the same user
  // Simplified implementation for now
  return false;
};

auditLogSchema.methods.markAsReviewed = function(reviewedBy, notes) {
  this.security.reviewedBy = reviewedBy;
  this.security.reviewedAt = new Date();
  this.security.reviewNotes = notes;
  this.security.requiresReview = false;
  
  return this.save();
};

// Static methods
auditLogSchema.statics.createLog = function(logData) {
  const auditLog = new this({
    eventType: logData.eventType,
    actor: {
      userId: logData.userId,
      userEmail: logData.userEmail,
      userName: logData.userName,
      userRole: logData.userRole,
      ipAddress: logData.ipAddress,
      userAgent: logData.userAgent,
      sessionId: logData.sessionId
    },
    target: logData.target || {},
    details: {
      action: logData.action,
      description: logData.description,
      metadata: logData.metadata,
      success: logData.success !== false, // default to true
      errorMessage: logData.errorMessage,
      errorCode: logData.errorCode
    },
    context: logData.context || {},
    geographic: logData.geographic || {},
    tags: logData.tags || []
  });
  
  return auditLog.save();
};

auditLogSchema.statics.getSecurityEvents = function(timeRange = 24) {
  const startTime = new Date(Date.now() - (timeRange * 60 * 60 * 1000));
  
  return this.find({
    timestamp: { $gte: startTime },
    'security.riskLevel': { $in: ['high', 'critical'] }
  }).sort({ timestamp: -1 });
};

auditLogSchema.statics.getUserActivity = function(userId, limit = 100) {
  return this.find({
    'actor.userId': userId
  })
  .sort({ timestamp: -1 })
  .limit(limit)
  .select('-system -checksum -signature');
};

auditLogSchema.statics.getElectionActivity = function(electionId, limit = 1000) {
  return this.find({
    'context.electionId': electionId
  })
  .sort({ timestamp: -1 })
  .limit(limit)
  .populate('actor.userId', 'name email role');
};

auditLogSchema.statics.getFailedAttempts = function(timeRange = 1) {
  const startTime = new Date(Date.now() - (timeRange * 60 * 60 * 1000));
  
  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: startTime },
        'details.success': false
      }
    },
    {
      $group: {
        _id: {
          eventType: '$eventType',
          ipAddress: '$actor.ipAddress'
        },
        count: { $sum: 1 },
        lastAttempt: { $max: '$timestamp' }
      }
    },
    {
      $match: {
        count: { $gte: 3 } // 3 or more failed attempts
      }
    },
    { $sort: { count: -1 } }
  ]);
};

auditLogSchema.statics.getSystemHealth = function() {
  const oneHour = new Date(Date.now() - (60 * 60 * 1000));
  
  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: oneHour }
      }
    },
    {
      $group: {
        _id: '$eventType',
        total: { $sum: 1 },
        successful: {
          $sum: { $cond: ['$details.success', 1, 0] }
        },
        failed: {
          $sum: { $cond: ['$details.success', 0, 1] }
        }
      }
    },
    {
      $addFields: {
        successRate: {
          $multiply: [
            { $divide: ['$successful', '$total'] },
            100
          ]
        }
      }
    },
    { $sort: { total: -1 } }
  ]);
};

// TTL index for automatic cleanup (optional)
auditLogSchema.index(
  { timestamp: 1 },
  { 
    expireAfterSeconds: 60 * 60 * 24 * 365 * 7, // 7 years
    partialFilterExpression: { 
      'compliance.legalHold': { $ne: true } 
    }
  }
);

module.exports = mongoose.model('AuditLog', auditLogSchema);
