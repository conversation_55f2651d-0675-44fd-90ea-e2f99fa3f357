import React, { createContext, useContext, useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  joinElection: (electionId: string) => void;
  leaveElection: (electionId: string) => void;
  emitVoteCast: (electionId: string, sessionId: string) => void;
  emitSuspiciousActivity: (electionId: string, sessionId: string, type: string, details: any) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const { user, token } = useAuth();

  useEffect(() => {
    if (user && token) {
      // Initialize socket connection
      const newSocket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5000', {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Connected to server');
        setConnected(true);
        toast.success('Connected to real-time updates');
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Disconnected from server:', reason);
        setConnected(false);
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          newSocket.connect();
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        setConnected(false);
        toast.error('Failed to connect to real-time updates');
      });

      // Election-specific event handlers
      newSocket.on('election-data', (data) => {
        console.log('Received election data:', data);
        // Handle initial election data
      });

      newSocket.on('election-data-update', (data) => {
        console.log('Election data updated:', data);
        // Handle real-time election updates
        toast.info(`Election updated: ${data.electionTitle}`);
      });

      newSocket.on('new-vote', (data) => {
        console.log('New vote cast:', data);
        // Handle new vote notifications
      });

      newSocket.on('election-status-change', (data) => {
        console.log('Election status changed:', data);
        toast.info(`Election "${data.title}" status changed to: ${data.status}`);
      });

      newSocket.on('security-alert', (data) => {
        console.warn('Security alert:', data);
        if (['admin', 'election_officer', 'super_admin'].includes(user.role)) {
          toast.warning(`Security Alert: ${data.type}`);
        }
      });

      newSocket.on('user-joined', (data) => {
        console.log('User joined election room:', data);
      });

      newSocket.on('user-left', (data) => {
        console.log('User left election room:', data);
      });

      newSocket.on('voting-session-started', (data) => {
        console.log('Voting session started:', data);
        if (['admin', 'election_officer'].includes(user.role)) {
          toast.info(`Voting session started by ${data.userName}`);
        }
      });

      newSocket.on('session-update', (data) => {
        console.log('Voting session update:', data);
      });

      newSocket.on('periodic-update', (data) => {
        console.log('Periodic update:', data);
        // Handle periodic updates (every 30 seconds)
      });

      newSocket.on('system-health', (data) => {
        console.log('System health:', data);
        // Handle system health data for admins
      });

      newSocket.on('error', (error) => {
        console.error('Socket error:', error);
        toast.error(`Connection error: ${error.message}`);
      });

      setSocket(newSocket);

      // Cleanup on unmount
      return () => {
        newSocket.close();
        setSocket(null);
        setConnected(false);
      };
    } else {
      // User not authenticated, close socket if exists
      if (socket) {
        socket.close();
        setSocket(null);
        setConnected(false);
      }
    }
  }, [user, token]);

  // Join election room for real-time updates
  const joinElection = (electionId: string) => {
    if (socket && connected) {
      socket.emit('join-election', { electionId });
      console.log(`Joining election room: ${electionId}`);
    }
  };

  // Leave election room
  const leaveElection = (electionId: string) => {
    if (socket && connected) {
      socket.emit('leave-election', { electionId });
      console.log(`Leaving election room: ${electionId}`);
    }
  };

  // Emit vote cast event
  const emitVoteCast = (electionId: string, sessionId: string) => {
    if (socket && connected) {
      socket.emit('vote-cast', { electionId, sessionId });
      console.log(`Vote cast emitted for election: ${electionId}`);
    }
  };

  // Emit suspicious activity
  const emitSuspiciousActivity = (electionId: string, sessionId: string, type: string, details: any) => {
    if (socket && connected) {
      socket.emit('suspicious-activity', {
        electionId,
        sessionId,
        type,
        details,
      });
      console.log(`Suspicious activity reported: ${type}`);
    }
  };

  const value: SocketContextType = {
    socket,
    connected,
    joinElection,
    leaveElection,
    emitVoteCast,
    emitSuspiciousActivity,
  };

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>;
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
