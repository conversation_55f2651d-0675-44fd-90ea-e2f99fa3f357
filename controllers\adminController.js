const { validationResult } = require('express-validator');
const User = require('../models/User');
const Election = require('../models/Election');
const Vote = require('../models/Vote');
const AuditLog = require('../models/AuditLog');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Get admin dashboard statistics
const getDashboardStats = catchAsync(async (req, res, next) => {
  try {
    // Get basic counts
    const totalUsers = await User.countDocuments();
    const eligibleVoters = await User.countDocuments({ isEligible: true });
    const totalElections = await Election.countDocuments();
    const activeElections = await Election.countDocuments({ status: 'active' });
    const totalVotes = await Vote.countDocuments({ isValid: true });

    // Get recent activity
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email createdAt isEligible');

    const recentElections = await Election.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title type status startDate endDate')
      .populate('createdBy', 'name email');

    // Get system health metrics
    const systemHealth = await AuditLog.getSystemHealth();

    // Get security alerts
    const securityAlerts = await AuditLog.getSecurityEvents(24); // Last 24 hours

    res.status(200).json({
      success: true,
      data: {
        statistics: {
          totalUsers,
          eligibleVoters,
          totalElections,
          activeElections,
          totalVotes,
          voterTurnout: totalUsers > 0 ? ((totalVotes / totalUsers) * 100).toFixed(2) : 0
        },
        recentActivity: {
          users: recentUsers,
          elections: recentElections
        },
        systemHealth,
        securityAlerts: securityAlerts.slice(0, 10) // Latest 10 alerts
      }
    });
  } catch (error) {
    logger.error('Failed to get dashboard stats:', error);
    return next(new AppError('Failed to retrieve dashboard statistics', 500));
  }
});

// Get all users with pagination and filters
const getUsers = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 20,
    search,
    role,
    isEligible,
    isActive,
    state,
    district
  } = req.query;

  // Build filter object
  const filter = {};
  
  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { voterIdNumber: { $regex: search, $options: 'i' } }
    ];
  }
  
  if (role) filter.role = role;
  if (isEligible !== undefined) filter.isEligible = isEligible === 'true';
  if (isActive !== undefined) filter.isActive = isActive === 'true';
  if (state) filter['address.state'] = state;
  if (district) filter['address.district'] = district;

  try {
    const users = await User.find(filter)
      .select('-password -aadhaarNumber -faceEmbedding -voicePrint')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const totalUsers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalUsers / limit);

    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get users:', error);
    return next(new AppError('Failed to retrieve users', 500));
  }
});

// Update user status
const updateUserStatus = catchAsync(async (req, res, next) => {
  const { userId } = req.params;
  const { isActive, isBlocked, isEligible, blockedReason } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Store previous state for audit
  const previousState = {
    isActive: user.isActive,
    isBlocked: user.isBlocked,
    isEligible: user.isEligible,
    blockedReason: user.blockedReason
  };

  // Update user status
  if (isActive !== undefined) user.isActive = isActive;
  if (isBlocked !== undefined) {
    user.isBlocked = isBlocked;
    if (isBlocked) {
      user.blockedAt = new Date();
      user.blockedReason = blockedReason || 'No reason provided';
    } else {
      user.blockedAt = undefined;
      user.blockedReason = undefined;
    }
  }
  if (isEligible !== undefined) user.isEligible = isEligible;

  user.lastModifiedBy = req.user._id;
  await user.save();

  // Create audit log
  await AuditLog.createLog({
    eventType: 'user_status_change',
    userId: req.user._id,
    userEmail: req.user.email,
    userName: req.user.name,
    userRole: req.user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'update_user_status',
    description: `Updated status for user: ${user.name}`,
    success: true,
    target: {
      resourceType: 'user',
      resourceId: user._id,
      resourceName: user.name,
      previousState,
      newState: {
        isActive: user.isActive,
        isBlocked: user.isBlocked,
        isEligible: user.isEligible,
        blockedReason: user.blockedReason
      }
    },
    context: {
      source: 'admin',
      component: 'admin_controller'
    }
  });

  logger.info(`User status updated by admin: ${req.user.email} for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'User status updated successfully',
    data: {
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        isActive: user.isActive,
        isBlocked: user.isBlocked,
        isEligible: user.isEligible,
        blockedReason: user.blockedReason
      }
    }
  });
});

// Create new election
const createElection = catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const {
    title,
    description,
    type,
    startDate,
    endDate,
    registrationDeadline,
    scope,
    geographicCoverage,
    settings,
    ballot
  } = req.body;

  try {
    const election = await Election.create({
      title,
      description,
      type,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      registrationDeadline: new Date(registrationDeadline),
      scope,
      geographicCoverage,
      settings: {
        allowMultipleVotes: false,
        requireFaceVerification: true,
        requireVoiceVerification: true,
        requireDocumentVerification: true,
        enableRealTimeResults: true,
        maxVotingDuration: 30,
        enableAuditLog: true,
        allowObservers: true,
        ...settings
      },
      ballot,
      createdBy: req.user._id,
      administrators: [{
        userId: req.user._id,
        role: 'super_admin',
        permissions: ['view', 'edit', 'delete', 'publish_results', 'manage_candidates', 'view_audit_logs'],
        assignedBy: req.user._id
      }]
    });

    // Create audit log
    await AuditLog.createLog({
      eventType: 'election_created',
      userId: req.user._id,
      userEmail: req.user.email,
      userName: req.user.name,
      userRole: req.user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'create_election',
      description: `Created new election: ${title}`,
      success: true,
      target: {
        resourceType: 'election',
        resourceId: election._id,
        resourceName: title
      },
      context: {
        electionId: election._id,
        electionTitle: title,
        source: 'admin',
        component: 'admin_controller'
      }
    });

    logger.election.created(election._id, title, req.user._id);

    res.status(201).json({
      success: true,
      message: 'Election created successfully',
      data: { election }
    });
  } catch (error) {
    logger.error('Failed to create election:', error);
    return next(new AppError('Failed to create election', 500));
  }
});

// Get all elections
const getElections = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 20,
    status,
    type,
    scope,
    search
  } = req.query;

  // Build filter object
  const filter = {};
  if (status) filter.status = status;
  if (type) filter.type = type;
  if (scope) filter.scope = scope;
  if (search) {
    filter.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  try {
    const elections = await Election.find(filter)
      .populate('createdBy', 'name email')
      .populate('administrators.userId', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const totalElections = await Election.countDocuments(filter);
    const totalPages = Math.ceil(totalElections / limit);

    res.status(200).json({
      success: true,
      data: {
        elections,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalElections,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get elections:', error);
    return next(new AppError('Failed to retrieve elections', 500));
  }
});

// Get single election
const getElection = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId)
    .populate('createdBy', 'name email')
    .populate('administrators.userId', 'name email role');

  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  res.status(200).json({
    success: true,
    data: { election }
  });
});

// Update election
const updateElection = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const updates = req.body;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission to edit
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('edit')
  );

  if (!hasPermission && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to edit this election', 403));
  }

  // Store previous state for audit
  const previousState = election.toObject();

  // Update election
  Object.keys(updates).forEach(key => {
    if (updates[key] !== undefined) {
      election[key] = updates[key];
    }
  });

  election.lastModifiedBy = req.user._id;
  await election.save();

  // Create audit log
  await AuditLog.createLog({
    eventType: 'election_modified',
    userId: req.user._id,
    userEmail: req.user.email,
    userName: req.user.name,
    userRole: req.user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'update_election',
    description: `Updated election: ${election.title}`,
    success: true,
    target: {
      resourceType: 'election',
      resourceId: election._id,
      resourceName: election.title,
      previousState,
      newState: election.toObject()
    },
    context: {
      electionId: election._id,
      electionTitle: election.title,
      source: 'admin',
      component: 'admin_controller'
    }
  });

  logger.info(`Election updated by admin: ${req.user.email}, Election: ${election.title}`);

  res.status(200).json({
    success: true,
    message: 'Election updated successfully',
    data: { election }
  });
});

// Add candidate to election
const addCandidate = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const candidateData = req.body;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission to manage candidates
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('manage_candidates')
  );

  if (!hasPermission && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to manage candidates for this election', 403));
  }

  try {
    await election.addCandidate(candidateData, req.user._id);

    // Create audit log
    await AuditLog.createLog({
      eventType: 'candidate_added',
      userId: req.user._id,
      userEmail: req.user.email,
      userName: req.user.name,
      userRole: req.user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'add_candidate',
      description: `Added candidate: ${candidateData.name} to election: ${election.title}`,
      success: true,
      target: {
        resourceType: 'candidate',
        resourceName: candidateData.name
      },
      context: {
        electionId: election._id,
        electionTitle: election.title,
        source: 'admin',
        component: 'admin_controller'
      }
    });

    logger.election.candidateAdded(election._id, candidateData.name, req.user._id);

    res.status(201).json({
      success: true,
      message: 'Candidate added successfully',
      data: { 
        election,
        addedCandidate: candidateData
      }
    });
  } catch (error) {
    logger.error('Failed to add candidate:', error);
    return next(new AppError('Failed to add candidate', 500));
  }
});

// Remove candidate from election
const removeCandidate = catchAsync(async (req, res, next) => {
  const { electionId, candidateId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission to manage candidates
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('manage_candidates')
  );

  if (!hasPermission && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to manage candidates for this election', 403));
  }

  // Find candidate before removal for audit log
  const candidate = election.candidates.find(c => c._id.toString() === candidateId);
  if (!candidate) {
    return next(new AppError('Candidate not found', 404));
  }

  try {
    await election.removeCandidate(candidateId);

    // Create audit log
    await AuditLog.createLog({
      eventType: 'candidate_removed',
      userId: req.user._id,
      userEmail: req.user.email,
      userName: req.user.name,
      userRole: req.user.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID,
      action: 'remove_candidate',
      description: `Removed candidate: ${candidate.name} from election: ${election.title}`,
      success: true,
      target: {
        resourceType: 'candidate',
        resourceId: candidateId,
        resourceName: candidate.name
      },
      context: {
        electionId: election._id,
        electionTitle: election.title,
        source: 'admin',
        component: 'admin_controller'
      }
    });

    logger.info(`Candidate removed by admin: ${req.user.email}, Candidate: ${candidate.name}`);

    res.status(200).json({
      success: true,
      message: 'Candidate removed successfully',
      data: { election }
    });
  } catch (error) {
    logger.error('Failed to remove candidate:', error);
    return next(new AppError('Failed to remove candidate', 500));
  }
});

// Start election
const startElection = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('edit')
  );

  if (!hasPermission && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to start this election', 403));
  }

  // Validate election can be started
  if (election.status !== 'scheduled') {
    return next(new AppError('Election can only be started from scheduled status', 400));
  }

  if (election.candidates.length === 0) {
    return next(new AppError('Cannot start election without candidates', 400));
  }

  // Update election status
  election.status = 'active';
  election.lastModifiedBy = req.user._id;
  await election.save();

  // Create audit log
  await AuditLog.createLog({
    eventType: 'election_started',
    userId: req.user._id,
    userEmail: req.user.email,
    userName: req.user.name,
    userRole: req.user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'start_election',
    description: `Started election: ${election.title}`,
    success: true,
    target: {
      resourceType: 'election',
      resourceId: election._id,
      resourceName: election.title
    },
    context: {
      electionId: election._id,
      electionTitle: election.title,
      source: 'admin',
      component: 'admin_controller'
    }
  });

  logger.election.started(election._id, election.title);

  res.status(200).json({
    success: true,
    message: 'Election started successfully',
    data: { election }
  });
});

// End election
const endElection = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('edit')
  );

  if (!hasPermission && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to end this election', 403));
  }

  // Validate election can be ended
  if (election.status !== 'active') {
    return next(new AppError('Only active elections can be ended', 400));
  }

  // Update election status
  election.status = 'completed';
  election.lastModifiedBy = req.user._id;
  await election.save();

  // Get final vote count
  const totalVotes = await Vote.countDocuments({
    electionId: election._id,
    isValid: true
  });

  // Create audit log
  await AuditLog.createLog({
    eventType: 'election_ended',
    userId: req.user._id,
    userEmail: req.user.email,
    userName: req.user.name,
    userRole: req.user.role,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    action: 'end_election',
    description: `Ended election: ${election.title}`,
    success: true,
    target: {
      resourceType: 'election',
      resourceId: election._id,
      resourceName: election.title
    },
    context: {
      electionId: election._id,
      electionTitle: election.title,
      source: 'admin',
      component: 'admin_controller'
    }
  });

  logger.election.ended(election._id, election.title, totalVotes);

  res.status(200).json({
    success: true,
    message: 'Election ended successfully',
    data: {
      election,
      totalVotes
    }
  });
});

module.exports = {
  getDashboardStats,
  getUsers,
  updateUserStatus,
  createElection,
  getElections,
  getElection,
  updateElection,
  addCandidate,
  removeCandidate,
  startElection,
  endElection
};
