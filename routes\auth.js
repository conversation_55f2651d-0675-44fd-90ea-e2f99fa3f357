const express = require('express');
const { body } = require('express-validator');
const {
  register,
  login,
  logout,
  verifyEmailOTP,
  verifyPhoneOTP,
  resendOTP,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword
} = require('../controllers/authController');
const { protect, sensitiveOperationLimit } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const registerValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  body('phone')
    .matches(/^[0-9]{10}$/)
    .withMessage('Please provide a valid 10-digit phone number'),
  
  body('aadhaarNumber')
    .matches(/^[0-9]{12}$/)
    .withMessage('Please provide a valid 12-digit Aadhaar number'),
  
  body('voterIdNumber')
    .matches(/^[A-Z]{3}[0-9]{7}$/)
    .withMessage('Please provide a valid Voter ID (format: **********)'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('address.street')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Street address must be between 5 and 200 characters'),
  
  body('address.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  
  body('address.district')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('District must be between 2 and 50 characters'),
  
  body('address.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('State must be between 2 and 50 characters'),
  
  body('address.pincode')
    .matches(/^[0-9]{6}$/)
    .withMessage('Please provide a valid 6-digit pincode'),
  
  body('address.constituency')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Constituency must be between 2 and 100 characters')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const otpValidation = [
  body('otp')
    .matches(/^[0-9]{6}$/)
    .withMessage('OTP must be a 6-digit number')
];

const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

const updateProfileValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('address.street')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Street address must be between 5 and 200 characters'),
  
  body('address.city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  
  body('address.district')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('District must be between 2 and 50 characters'),
  
  body('address.state')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('State must be between 2 and 50 characters'),
  
  body('address.pincode')
    .optional()
    .matches(/^[0-9]{6}$/)
    .withMessage('Please provide a valid 6-digit pincode'),
  
  body('address.constituency')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Constituency must be between 2 and 100 characters')
];

// Public routes
router.post('/register', sensitiveOperationLimit(3, 15 * 60 * 1000), registerValidation, register);
router.post('/login', sensitiveOperationLimit(5, 15 * 60 * 1000), loginValidation, login);
router.post('/verify-email', otpValidation, verifyEmailOTP);
router.post('/verify-phone', otpValidation, verifyPhoneOTP);
router.post('/resend-otp', sensitiveOperationLimit(3, 5 * 60 * 1000), resendOTP);
router.post('/forgot-password', sensitiveOperationLimit(3, 15 * 60 * 1000), forgotPassword);
router.post('/reset-password/:token', sensitiveOperationLimit(3, 15 * 60 * 1000), resetPassword);

// Protected routes
router.use(protect); // All routes below this middleware are protected

router.post('/logout', logout);
router.get('/profile', getProfile);
router.put('/profile', updateProfileValidation, updateProfile);
router.put('/change-password', changePasswordValidation, changePassword);

// Health check for authenticated users
router.get('/me', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      user: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
        isEligible: req.user.isEligible,
        verificationStatus: {
          email: req.user.isEmailVerified,
          phone: req.user.isPhoneVerified,
          document: req.user.isDocumentVerified,
          face: req.user.isFaceVerified
        }
      }
    }
  });
});

module.exports = router;
