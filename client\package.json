{"name": "ai-voting-system-client", "version": "1.0.0", "description": "AI-Enabled Online Voting System - Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^2.1.4", "axios": "^1.3.0", "socket.io-client": "^4.6.0", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/x-charts": "^6.0.0", "@mui/x-data-grid": "^6.0.0", "recharts": "^2.5.0", "react-webcam": "^7.0.1", "react-audio-recorder": "^1.0.0", "react-dropzone": "^14.2.0", "react-hook-form": "^7.43.0", "react-query": "^3.39.0", "react-toastify": "^9.1.0", "date-fns": "^2.29.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.191"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}