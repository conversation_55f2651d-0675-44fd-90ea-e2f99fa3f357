const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const logger = require('./logger');

class VoiceDetectionService {
  constructor() {
    this.threshold = parseFloat(process.env.VOICE_DETECTION_THRESHOLD) || 0.7;
    this.isInitialized = false;
    this.pythonPath = process.env.PYTHON_PATH || 'python';
  }

  // Initialize the voice detection service
  async initialize() {
    try {
      // Check if Python and required libraries are available
      const isAvailable = await this.checkPythonEnvironment();
      
      if (!isAvailable) {
        logger.warn('Python environment not properly configured for voice detection');
        // Fall back to basic audio analysis
      }

      this.isInitialized = true;
      logger.info('Voice detection service initialized');
    } catch (error) {
      logger.error('Failed to initialize voice detection service:', error);
      throw new Error('Voice detection initialization failed');
    }
  }

  // Check if Python environment is properly configured
  async checkPythonEnvironment() {
    return new Promise((resolve) => {
      const python = spawn(this.pythonPath, ['-c', 'import librosa, numpy; print("OK")']);
      
      python.stdout.on('data', (data) => {
        if (data.toString().trim() === 'OK') {
          resolve(true);
        }
      });

      python.stderr.on('data', (data) => {
        logger.warn('Python environment check failed:', data.toString());
        resolve(false);
      });

      python.on('close', (code) => {
        if (code !== 0) {
          resolve(false);
        }
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        python.kill();
        resolve(false);
      }, 5000);
    });
  }

  // Analyze audio file for voice characteristics
  async analyzeAudio(audioPath) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Check if file exists
      if (!fs.existsSync(audioPath)) {
        throw new Error('Audio file not found');
      }

      // Get basic audio information
      const audioInfo = await this.getAudioInfo(audioPath);
      
      // Perform voice analysis
      const voiceAnalysis = await this.performVoiceAnalysis(audioPath);
      
      // Detect multiple speakers
      const speakerAnalysis = await this.detectMultipleSpeakers(audioPath);
      
      return {
        success: true,
        audioInfo: audioInfo,
        voiceAnalysis: voiceAnalysis,
        speakerAnalysis: speakerAnalysis,
        multipleVoicesDetected: speakerAnalysis.speakerCount > 1,
        confidence: voiceAnalysis.confidence
      };
    } catch (error) {
      logger.error('Audio analysis failed:', error);
      return {
        success: false,
        error: error.message,
        multipleVoicesDetected: false,
        confidence: 0
      };
    }
  }

  // Get basic audio file information
  async getAudioInfo(audioPath) {
    return new Promise((resolve, reject) => {
      // Use ffprobe to get audio information
      const ffprobe = spawn('ffprobe', [
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        '-show_streams',
        audioPath
      ]);

      let output = '';
      
      ffprobe.stdout.on('data', (data) => {
        output += data.toString();
      });

      ffprobe.stderr.on('data', (data) => {
        logger.warn('ffprobe stderr:', data.toString());
      });

      ffprobe.on('close', (code) => {
        if (code === 0) {
          try {
            const info = JSON.parse(output);
            const audioStream = info.streams.find(s => s.codec_type === 'audio');
            
            resolve({
              duration: parseFloat(info.format.duration),
              bitRate: parseInt(info.format.bit_rate),
              sampleRate: audioStream ? parseInt(audioStream.sample_rate) : null,
              channels: audioStream ? parseInt(audioStream.channels) : null,
              codec: audioStream ? audioStream.codec_name : null,
              size: parseInt(info.format.size)
            });
          } catch (error) {
            // Fallback to basic file info
            const stats = fs.statSync(audioPath);
            resolve({
              duration: null,
              bitRate: null,
              sampleRate: null,
              channels: null,
              codec: null,
              size: stats.size
            });
          }
        } else {
          // Fallback to basic file info
          const stats = fs.statSync(audioPath);
          resolve({
            duration: null,
            bitRate: null,
            sampleRate: null,
            channels: null,
            codec: null,
            size: stats.size
          });
        }
      });
    });
  }

  // Perform voice analysis using Python script
  async performVoiceAnalysis(audioPath) {
    return new Promise((resolve) => {
      const scriptPath = path.join(__dirname, '../python/voice_analysis.py');
      
      // Check if Python script exists
      if (!fs.existsSync(scriptPath)) {
        // Fallback to basic analysis
        resolve(this.basicVoiceAnalysis(audioPath));
        return;
      }

      const python = spawn(this.pythonPath, [scriptPath, audioPath]);
      
      let output = '';
      let errorOutput = '';
      
      python.stdout.on('data', (data) => {
        output += data.toString();
      });

      python.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      python.on('close', (code) => {
        if (code === 0 && output.trim()) {
          try {
            const result = JSON.parse(output.trim());
            resolve(result);
          } catch (error) {
            logger.warn('Failed to parse Python voice analysis output:', error);
            resolve(this.basicVoiceAnalysis(audioPath));
          }
        } else {
          logger.warn('Python voice analysis failed:', errorOutput);
          resolve(this.basicVoiceAnalysis(audioPath));
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        python.kill();
        resolve(this.basicVoiceAnalysis(audioPath));
      }, 30000);
    });
  }

  // Basic voice analysis fallback
  async basicVoiceAnalysis(audioPath) {
    try {
      const stats = fs.statSync(audioPath);
      
      // Basic heuristics based on file size and duration
      const estimatedDuration = stats.size / (16000 * 2); // Assume 16kHz, 16-bit
      
      return {
        confidence: 0.5, // Low confidence for basic analysis
        voiceActivity: estimatedDuration > 1 ? 0.8 : 0.3,
        speechQuality: 0.6,
        noiseLevel: 0.4,
        method: 'basic_heuristic'
      };
    } catch (error) {
      return {
        confidence: 0,
        voiceActivity: 0,
        speechQuality: 0,
        noiseLevel: 1,
        method: 'error'
      };
    }
  }

  // Detect multiple speakers
  async detectMultipleSpeakers(audioPath) {
    return new Promise((resolve) => {
      const scriptPath = path.join(__dirname, '../python/speaker_detection.py');
      
      // Check if Python script exists
      if (!fs.existsSync(scriptPath)) {
        // Fallback to basic analysis
        resolve(this.basicSpeakerDetection(audioPath));
        return;
      }

      const python = spawn(this.pythonPath, [scriptPath, audioPath]);
      
      let output = '';
      let errorOutput = '';
      
      python.stdout.on('data', (data) => {
        output += data.toString();
      });

      python.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      python.on('close', (code) => {
        if (code === 0 && output.trim()) {
          try {
            const result = JSON.parse(output.trim());
            resolve(result);
          } catch (error) {
            logger.warn('Failed to parse Python speaker detection output:', error);
            resolve(this.basicSpeakerDetection(audioPath));
          }
        } else {
          logger.warn('Python speaker detection failed:', errorOutput);
          resolve(this.basicSpeakerDetection(audioPath));
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        python.kill();
        resolve(this.basicSpeakerDetection(audioPath));
      }, 30000);
    });
  }

  // Basic speaker detection fallback
  async basicSpeakerDetection(audioPath) {
    try {
      const audioInfo = await this.getAudioInfo(audioPath);
      
      // Basic heuristics
      let speakerCount = 1;
      let confidence = 0.5;
      
      // If stereo and significant duration, might indicate multiple speakers
      if (audioInfo.channels === 2 && audioInfo.duration > 10) {
        speakerCount = 1; // Conservative estimate
        confidence = 0.3;
      }
      
      return {
        speakerCount: speakerCount,
        confidence: confidence,
        segments: [{
          start: 0,
          end: audioInfo.duration || 10,
          speaker: 'speaker_1'
        }],
        method: 'basic_heuristic'
      };
    } catch (error) {
      return {
        speakerCount: 1,
        confidence: 0,
        segments: [],
        method: 'error'
      };
    }
  }

  // Extract voice features for comparison
  async extractVoiceFeatures(audioPath) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const scriptPath = path.join(__dirname, '../python/voice_features.py');
      
      if (!fs.existsSync(scriptPath)) {
        return this.basicVoiceFeatures(audioPath);
      }

      return new Promise((resolve) => {
        const python = spawn(this.pythonPath, [scriptPath, audioPath]);
        
        let output = '';
        let errorOutput = '';
        
        python.stdout.on('data', (data) => {
          output += data.toString();
        });

        python.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        python.on('close', (code) => {
          if (code === 0 && output.trim()) {
            try {
              const result = JSON.parse(output.trim());
              resolve({
                success: true,
                features: result.features,
                method: result.method || 'python_analysis'
              });
            } catch (error) {
              logger.warn('Failed to parse voice features output:', error);
              resolve(this.basicVoiceFeatures(audioPath));
            }
          } else {
            logger.warn('Voice features extraction failed:', errorOutput);
            resolve(this.basicVoiceFeatures(audioPath));
          }
        });

        // Timeout after 30 seconds
        setTimeout(() => {
          python.kill();
          resolve(this.basicVoiceFeatures(audioPath));
        }, 30000);
      });
    } catch (error) {
      logger.error('Voice features extraction failed:', error);
      return {
        success: false,
        features: null,
        error: error.message
      };
    }
  }

  // Basic voice features fallback
  async basicVoiceFeatures(audioPath) {
    try {
      const audioInfo = await this.getAudioInfo(audioPath);
      const stats = fs.statSync(audioPath);
      
      // Create basic feature vector based on file characteristics
      const features = [
        audioInfo.duration || 0,
        audioInfo.sampleRate || 0,
        audioInfo.channels || 0,
        stats.size,
        audioInfo.bitRate || 0
      ];
      
      // Pad or truncate to standard size
      while (features.length < 128) {
        features.push(0);
      }
      
      return {
        success: true,
        features: features.slice(0, 128),
        method: 'basic_file_stats'
      };
    } catch (error) {
      return {
        success: false,
        features: new Array(128).fill(0),
        method: 'error'
      };
    }
  }

  // Compare voice features
  compareVoiceFeatures(features1, features2) {
    try {
      if (!features1 || !features2 || features1.length !== features2.length) {
        return {
          success: false,
          similarity: 0,
          isMatch: false,
          error: 'Invalid features'
        };
      }

      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(features1, features2);
      const isMatch = similarity >= this.threshold;

      return {
        success: true,
        similarity: similarity,
        isMatch: isMatch,
        threshold: this.threshold,
        confidence: similarity
      };
    } catch (error) {
      logger.error('Voice comparison failed:', error);
      return {
        success: false,
        similarity: 0,
        isMatch: false,
        error: error.message
      };
    }
  }

  // Calculate cosine similarity between two vectors
  cosineSimilarity(vec1, vec2) {
    try {
      let dotProduct = 0;
      let norm1 = 0;
      let norm2 = 0;

      for (let i = 0; i < vec1.length; i++) {
        dotProduct += vec1[i] * vec2[i];
        norm1 += vec1[i] * vec1[i];
        norm2 += vec2[i] * vec2[i];
      }

      norm1 = Math.sqrt(norm1);
      norm2 = Math.sqrt(norm2);

      if (norm1 === 0 || norm2 === 0) {
        return 0;
      }

      return dotProduct / (norm1 * norm2);
    } catch (error) {
      logger.error('Cosine similarity calculation failed:', error);
      return 0;
    }
  }

  // Verify voice against stored features
  async verifyVoice(audioPath, storedFeatures) {
    try {
      // Analyze the audio
      const audioAnalysis = await this.analyzeAudio(audioPath);
      
      if (!audioAnalysis.success) {
        return {
          success: false,
          isMatch: false,
          confidence: 0,
          error: 'Audio analysis failed'
        };
      }

      // Check for multiple voices
      if (audioAnalysis.multipleVoicesDetected) {
        return {
          success: true,
          isMatch: false,
          confidence: 0,
          multipleVoicesDetected: true,
          error: 'Multiple voices detected'
        };
      }

      // Extract voice features
      const featuresResult = await this.extractVoiceFeatures(audioPath);
      
      if (!featuresResult.success) {
        return {
          success: false,
          isMatch: false,
          confidence: 0,
          error: 'Failed to extract voice features'
        };
      }

      // Compare with stored features
      const comparison = this.compareVoiceFeatures(featuresResult.features, storedFeatures);

      return {
        success: true,
        isMatch: comparison.isMatch,
        confidence: comparison.similarity,
        multipleVoicesDetected: false,
        voiceQuality: audioAnalysis.voiceAnalysis.speechQuality,
        details: {
          audioAnalysis: audioAnalysis,
          comparison: comparison
        }
      };
    } catch (error) {
      logger.error('Voice verification failed:', error);
      return {
        success: false,
        isMatch: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  // Real-time voice monitoring
  async startVoiceMonitoring(callback) {
    try {
      // This would typically use WebRTC or similar for real-time audio
      // For now, return a mock monitoring session
      const sessionId = Date.now().toString();
      
      logger.info(`Started voice monitoring session: ${sessionId}`);
      
      return {
        success: true,
        sessionId: sessionId,
        stop: () => {
          logger.info(`Stopped voice monitoring session: ${sessionId}`);
        }
      };
    } catch (error) {
      logger.error('Failed to start voice monitoring:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
const voiceDetectionService = new VoiceDetectionService();
module.exports = voiceDetectionService;
