const express = require('express');
const { body } = require('express-validator');
const {
  getDashboardStats,
  getUsers,
  updateUserStatus,
  createElection,
  getElections,
  getElection,
  updateElection,
  addCandidate,
  removeCandidate,
  startElection,
  endElection
} = require('../controllers/adminController');
const { protect, authorize, checkIPWhitelist, sensitiveOperationLimit } = require('../middleware/auth');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(protect);
router.use(authorize('admin', 'election_officer', 'super_admin'));

// Optional IP whitelist check for admin routes
if (process.env.ADMIN_IP_WHITELIST) {
  router.use(checkIPWhitelist);
}

// Validation rules for election creation
const createElectionValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Title must be between 5 and 200 characters'),
  
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  
  body('type')
    .isIn(['general', 'state', 'local', 'referendum', 'special'])
    .withMessage('Invalid election type'),
  
  body('startDate')
    .isISO8601()
    .withMessage('Invalid start date format'),
  
  body('endDate')
    .isISO8601()
    .withMessage('Invalid end date format')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  body('registrationDeadline')
    .isISO8601()
    .withMessage('Invalid registration deadline format')
    .custom((value, { req }) => {
      if (new Date(value) >= new Date(req.body.startDate)) {
        throw new Error('Registration deadline must be before start date');
      }
      return true;
    }),
  
  body('scope')
    .isIn(['national', 'state', 'district', 'constituency', 'local'])
    .withMessage('Invalid election scope'),
  
  body('geographicCoverage.states')
    .optional()
    .isArray()
    .withMessage('States must be an array'),
  
  body('geographicCoverage.constituencies')
    .optional()
    .isArray()
    .withMessage('Constituencies must be an array')
];

// Validation rules for adding candidates
const addCandidateValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Candidate name must be between 2 and 100 characters'),
  
  body('party.name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Party name must be between 2 and 100 characters'),
  
  body('constituency')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Constituency must be between 2 and 100 characters'),
  
  body('biography')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Biography cannot exceed 1000 characters')
];

// Dashboard and Statistics
router.get('/dashboard', getDashboardStats);

// User Management
router.get('/users', getUsers);
router.put(
  '/users/:userId/status',
  sensitiveOperationLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  updateUserStatus
);

// Election Management
router.get('/elections', getElections);
router.get('/elections/:electionId', getElection);
router.post(
  '/elections',
  sensitiveOperationLimit(3, 60 * 60 * 1000), // 3 elections per hour
  createElectionValidation,
  createElection
);
router.put(
  '/elections/:electionId',
  sensitiveOperationLimit(10, 15 * 60 * 1000), // 10 updates per 15 minutes
  updateElection
);

// Election Status Management
router.post(
  '/elections/:electionId/start',
  sensitiveOperationLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  startElection
);
router.post(
  '/elections/:electionId/end',
  sensitiveOperationLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
  endElection
);

// Candidate Management
router.post(
  '/elections/:electionId/candidates',
  sensitiveOperationLimit(10, 15 * 60 * 1000), // 10 candidates per 15 minutes
  addCandidateValidation,
  addCandidate
);
router.delete(
  '/elections/:electionId/candidates/:candidateId',
  sensitiveOperationLimit(5, 15 * 60 * 1000), // 5 deletions per 15 minutes
  removeCandidate
);

module.exports = router;
