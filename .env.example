# Database Configuration
MONGODB_URI=mongodb://localhost:27017/voting-system

# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=24h
REFRESH_TOKEN_SECRET=your-refresh-token-secret-here

# Session Configuration
SESSION_SECRET=your-session-secret-here

# Email Configuration (for OTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# AI/ML Configuration
FACE_RECOGNITION_THRESHOLD=0.6
VOICE_DETECTION_THRESHOLD=0.7

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here
ENCRYPTION_IV=your-16-character-iv-here

# Admin Configuration
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=SuperSecurePassword123!

# Geographic API (Optional)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
