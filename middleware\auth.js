const jwt = require('jsonwebtoken');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const logger = require('../utils/logger');

// Generate JWT token
const generateToken = (userId, role) => {
  return jwt.sign(
    { userId, role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '24h' }
  );
};

// Generate refresh token
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { userId, type: 'refresh' },
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: '7d' }
  );
};

// Middleware to protect routes
const protect = async (req, res, next) => {
  try {
    let token;

    // Get token from header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await User.findById(decoded.userId).select('-password');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'Account is deactivated'
        });
      }

      // Check if user is blocked
      if (user.isBlocked) {
        return res.status(401).json({
          success: false,
          message: 'Account is blocked'
        });
      }

      // Check if account is locked
      if (user.isLocked) {
        return res.status(401).json({
          success: false,
          message: 'Account is temporarily locked due to multiple failed login attempts'
        });
      }

      // Add user to request object
      req.user = user;
      
      // Log successful authentication
      logger.security.dataAccess(
        user._id,
        req.originalUrl,
        req.method,
        req.ip
      );

      next();
    } catch (error) {
      logger.error('Token verification failed:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during authentication'
    });
  }
};

// Middleware to authorize specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    if (!roles.includes(req.user.role)) {
      logger.security.suspiciousActivity(
        req.user._id,
        'unauthorized_access_attempt',
        { 
          requiredRoles: roles,
          userRole: req.user.role,
          endpoint: req.originalUrl
        },
        req.ip
      );

      return res.status(403).json({
        success: false,
        message: `User role '${req.user.role}' is not authorized to access this resource`
      });
    }

    next();
  };
};

// Middleware to check if user is eligible to vote
const checkVotingEligibility = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user is eligible to vote
    if (!req.user.isEligible) {
      return res.status(403).json({
        success: false,
        message: 'User is not eligible to vote'
      });
    }

    // Check if all verifications are complete
    const requiredVerifications = [
      'isEmailVerified',
      'isPhoneVerified',
      'isDocumentVerified',
      'isFaceVerified'
    ];

    const missingVerifications = requiredVerifications.filter(
      verification => !req.user[verification]
    );

    if (missingVerifications.length > 0) {
      return res.status(403).json({
        success: false,
        message: 'Complete verification required',
        missingVerifications: missingVerifications.map(v => v.replace('is', '').replace('Verified', ''))
      });
    }

    next();
  } catch (error) {
    logger.error('Voting eligibility check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during eligibility check'
    });
  }
};

// Middleware to log API requests
const logRequest = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    
    logger.performance.apiCall(
      req.method,
      req.originalUrl,
      duration,
      res.statusCode,
      req.user ? req.user._id : null
    );

    // Create audit log for sensitive operations
    if (req.user && shouldAuditRequest(req)) {
      AuditLog.createLog({
        eventType: getEventTypeFromRequest(req),
        userId: req.user._id,
        userEmail: req.user.email,
        userName: req.user.name,
        userRole: req.user.role,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        sessionId: req.sessionID,
        action: `${req.method} ${req.originalUrl}`,
        description: `API request to ${req.originalUrl}`,
        success: res.statusCode < 400,
        context: {
          source: 'api',
          component: 'middleware'
        }
      }).catch(error => {
        logger.error('Failed to create audit log:', error);
      });
    }

    originalEnd.apply(this, args);
  };

  next();
};

// Helper function to determine if request should be audited
const shouldAuditRequest = (req) => {
  const auditPaths = [
    '/api/auth',
    '/api/voting',
    '/api/admin',
    '/api/upload'
  ];

  return auditPaths.some(path => req.originalUrl.startsWith(path));
};

// Helper function to get event type from request
const getEventTypeFromRequest = (req) => {
  if (req.originalUrl.includes('/auth/login')) return 'user_login';
  if (req.originalUrl.includes('/auth/logout')) return 'user_logout';
  if (req.originalUrl.includes('/auth/register')) return 'user_registration';
  if (req.originalUrl.includes('/voting/cast')) return 'vote_cast';
  if (req.originalUrl.includes('/admin/elections')) return 'election_modified';
  if (req.originalUrl.includes('/upload')) return 'document_upload';
  return 'system_access';
};

// Middleware to validate session
const validateSession = async (req, res, next) => {
  try {
    if (!req.session || !req.sessionID) {
      return res.status(401).json({
        success: false,
        message: 'Invalid session'
      });
    }

    // Check session expiry
    if (req.session.cookie && req.session.cookie.expires) {
      if (new Date() > new Date(req.session.cookie.expires)) {
        req.session.destroy();
        return res.status(401).json({
          success: false,
          message: 'Session expired'
        });
      }
    }

    // Validate session data integrity
    if (req.user && req.session.userId && req.session.userId !== req.user._id.toString()) {
      logger.security.suspiciousActivity(
        req.user._id,
        'session_mismatch',
        {
          sessionUserId: req.session.userId,
          tokenUserId: req.user._id.toString()
        },
        req.ip
      );

      req.session.destroy();
      return res.status(401).json({
        success: false,
        message: 'Session validation failed'
      });
    }

    next();
  } catch (error) {
    logger.error('Session validation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during session validation'
    });
  }
};

// Middleware to check IP whitelist for admin routes
const checkIPWhitelist = (req, res, next) => {
  const adminWhitelist = process.env.ADMIN_IP_WHITELIST;
  
  if (!adminWhitelist) {
    return next(); // No whitelist configured
  }

  const allowedIPs = adminWhitelist.split(',').map(ip => ip.trim());
  const clientIP = req.ip || req.connection.remoteAddress;

  if (!allowedIPs.includes(clientIP)) {
    logger.security.suspiciousActivity(
      req.user ? req.user._id : 'unknown',
      'ip_not_whitelisted',
      {
        clientIP,
        allowedIPs: allowedIPs.length,
        endpoint: req.originalUrl
      },
      clientIP
    );

    return res.status(403).json({
      success: false,
      message: 'Access denied from this IP address'
    });
  }

  next();
};

// Rate limiting middleware for sensitive operations
const sensitiveOperationLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map();

  return (req, res, next) => {
    const key = `${req.ip}:${req.user ? req.user._id : 'anonymous'}`;
    const now = Date.now();
    
    // Clean old entries
    for (const [k, data] of attempts.entries()) {
      if (now - data.firstAttempt > windowMs) {
        attempts.delete(k);
      }
    }

    const userAttempts = attempts.get(key);
    
    if (!userAttempts) {
      attempts.set(key, { count: 1, firstAttempt: now });
      return next();
    }

    if (userAttempts.count >= maxAttempts) {
      logger.security.suspiciousActivity(
        req.user ? req.user._id : 'unknown',
        'rate_limit_exceeded',
        {
          attempts: userAttempts.count,
          endpoint: req.originalUrl,
          windowMs
        },
        req.ip
      );

      return res.status(429).json({
        success: false,
        message: 'Too many attempts. Please try again later.',
        retryAfter: Math.ceil((windowMs - (now - userAttempts.firstAttempt)) / 1000)
      });
    }

    userAttempts.count++;
    next();
  };
};

module.exports = {
  generateToken,
  generateRefreshToken,
  protect,
  authorize,
  checkVotingEligibility,
  logRequest,
  validateSession,
  checkIPWhitelist,
  sensitiveOperationLimit
};
