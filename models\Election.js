const mongoose = require('mongoose');

const electionSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Election title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Election description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  type: {
    type: String,
    enum: ['general', 'state', 'local', 'referendum', 'special'],
    required: [true, 'Election type is required']
  },
  
  // Timing
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  registrationDeadline: {
    type: Date,
    required: [true, 'Registration deadline is required']
  },
  
  // Geographic Scope
  scope: {
    type: String,
    enum: ['national', 'state', 'district', 'constituency', 'local'],
    required: [true, 'Election scope is required']
  },
  geographicCoverage: {
    states: [String],
    districts: [String],
    constituencies: [String],
    cities: [String],
    villages: [String]
  },
  
  // Status
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'active', 'completed', 'cancelled', 'suspended'],
    default: 'draft'
  },
  
  // Configuration
  settings: {
    allowMultipleVotes: { type: Boolean, default: false },
    requireFaceVerification: { type: Boolean, default: true },
    requireVoiceVerification: { type: Boolean, default: true },
    requireDocumentVerification: { type: Boolean, default: true },
    enableRealTimeResults: { type: Boolean, default: true },
    maxVotingDuration: { type: Number, default: 30 }, // minutes
    enableAuditLog: { type: Boolean, default: true },
    allowObservers: { type: Boolean, default: true }
  },
  
  // Ballot Configuration
  ballot: {
    title: String,
    instructions: String,
    layout: {
      type: String,
      enum: ['single-column', 'multi-column', 'grid'],
      default: 'single-column'
    },
    showCandidatePhotos: { type: Boolean, default: true },
    showPartyLogos: { type: Boolean, default: true },
    randomizeCandidateOrder: { type: Boolean, default: false }
  },
  
  // Candidates/Options
  candidates: [{
    name: {
      type: String,
      required: [true, 'Candidate name is required']
    },
    party: {
      name: String,
      logo: String, // File path or URL
      color: String // Hex color code
    },
    photo: String, // File path or URL
    biography: String,
    manifesto: String,
    constituency: String,
    position: Number, // Display order
    isActive: { type: Boolean, default: true },
    addedAt: { type: Date, default: Date.now },
    addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  
  // Results
  results: {
    totalVotes: { type: Number, default: 0 },
    validVotes: { type: Number, default: 0 },
    invalidVotes: { type: Number, default: 0 },
    candidateResults: [{
      candidateId: mongoose.Schema.Types.ObjectId,
      candidateName: String,
      party: String,
      votes: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 }
    }],
    geographicResults: {
      stateWise: [{
        state: String,
        totalVotes: Number,
        results: [{
          candidateId: mongoose.Schema.Types.ObjectId,
          votes: Number,
          percentage: Number
        }]
      }],
      districtWise: [{
        district: String,
        state: String,
        totalVotes: Number,
        results: [{
          candidateId: mongoose.Schema.Types.ObjectId,
          votes: Number,
          percentage: Number
        }]
      }],
      constituencyWise: [{
        constituency: String,
        district: String,
        state: String,
        totalVotes: Number,
        results: [{
          candidateId: mongoose.Schema.Types.ObjectId,
          votes: Number,
          percentage: Number
        }]
      }]
    },
    winner: {
      candidateId: mongoose.Schema.Types.ObjectId,
      candidateName: String,
      party: String,
      votes: Number,
      percentage: Number
    },
    isResultPublished: { type: Boolean, default: false },
    publishedAt: Date,
    publishedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  },
  
  // Statistics
  statistics: {
    totalEligibleVoters: { type: Number, default: 0 },
    totalRegisteredVoters: { type: Number, default: 0 },
    voterTurnout: { type: Number, default: 0 },
    voterTurnoutPercentage: { type: Number, default: 0 },
    averageVotingTime: { type: Number, default: 0 }, // in minutes
    peakVotingHour: String,
    geographicTurnout: {
      stateWise: [{
        state: String,
        eligibleVoters: Number,
        actualVoters: Number,
        turnoutPercentage: Number
      }],
      districtWise: [{
        district: String,
        state: String,
        eligibleVoters: Number,
        actualVoters: Number,
        turnoutPercentage: Number
      }]
    }
  },
  
  // Security & Audit
  securitySettings: {
    encryptionEnabled: { type: Boolean, default: true },
    auditLogLevel: {
      type: String,
      enum: ['basic', 'detailed', 'comprehensive'],
      default: 'comprehensive'
    },
    suspiciousActivityThreshold: { type: Number, default: 5 },
    autoSuspendOnAnomaly: { type: Boolean, default: true }
  },
  
  // Access Control
  administrators: [{
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    role: {
      type: String,
      enum: ['super_admin', 'election_officer', 'observer', 'analyst'],
      required: true
    },
    permissions: [{
      type: String,
      enum: ['view', 'edit', 'delete', 'publish_results', 'manage_candidates', 'view_audit_logs']
    }],
    assignedAt: { type: Date, default: Date.now },
    assignedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastModifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
});

// Indexes for performance
electionSchema.index({ status: 1, startDate: 1 });
electionSchema.index({ 'geographicCoverage.states': 1 });
electionSchema.index({ 'geographicCoverage.constituencies': 1 });
electionSchema.index({ type: 1, scope: 1 });
electionSchema.index({ startDate: 1, endDate: 1 });

// Virtual properties
electionSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'active' && this.startDate <= now && this.endDate >= now;
});

electionSchema.virtual('isUpcoming').get(function() {
  return this.status === 'scheduled' && this.startDate > new Date();
});

electionSchema.virtual('isCompleted').get(function() {
  return this.status === 'completed' || this.endDate < new Date();
});

electionSchema.virtual('duration').get(function() {
  return this.endDate - this.startDate;
});

// Pre-save middleware
electionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Validate dates
  if (this.startDate >= this.endDate) {
    return next(new Error('Start date must be before end date'));
  }
  
  if (this.registrationDeadline >= this.startDate) {
    return next(new Error('Registration deadline must be before start date'));
  }
  
  next();
});

// Instance methods
electionSchema.methods.addCandidate = function(candidateData, addedBy) {
  const candidate = {
    ...candidateData,
    position: this.candidates.length + 1,
    addedAt: new Date(),
    addedBy: addedBy
  };
  
  this.candidates.push(candidate);
  return this.save();
};

electionSchema.methods.removeCandidate = function(candidateId) {
  this.candidates = this.candidates.filter(
    candidate => candidate._id.toString() !== candidateId.toString()
  );
  
  // Reorder positions
  this.candidates.forEach((candidate, index) => {
    candidate.position = index + 1;
  });
  
  return this.save();
};

electionSchema.methods.updateResults = function(voteData) {
  // Update total votes
  this.results.totalVotes += 1;
  this.results.validVotes += 1;
  
  // Update candidate results
  const candidateResult = this.results.candidateResults.find(
    result => result.candidateId.toString() === voteData.candidateId.toString()
  );
  
  if (candidateResult) {
    candidateResult.votes += 1;
  } else {
    const candidate = this.candidates.find(
      c => c._id.toString() === voteData.candidateId.toString()
    );
    
    if (candidate) {
      this.results.candidateResults.push({
        candidateId: voteData.candidateId,
        candidateName: candidate.name,
        party: candidate.party.name,
        votes: 1,
        percentage: 0
      });
    }
  }
  
  // Recalculate percentages
  this.results.candidateResults.forEach(result => {
    result.percentage = (result.votes / this.results.validVotes) * 100;
  });
  
  // Update winner
  const winner = this.results.candidateResults.reduce((prev, current) => 
    (prev.votes > current.votes) ? prev : current
  );
  
  this.results.winner = {
    candidateId: winner.candidateId,
    candidateName: winner.candidateName,
    party: winner.party,
    votes: winner.votes,
    percentage: winner.percentage
  };
  
  return this.save();
};

electionSchema.methods.canUserVote = function(user) {
  const now = new Date();
  
  // Check if election is active
  if (!this.isActive) {
    return { canVote: false, reason: 'Election is not currently active' };
  }
  
  // Check if user is eligible
  if (!user.isEligible) {
    return { canVote: false, reason: 'User is not eligible to vote' };
  }
  
  // Check if user has already voted
  const hasVoted = user.votingHistory.some(
    vote => vote.electionId.toString() === this._id.toString()
  );
  
  if (hasVoted && !this.settings.allowMultipleVotes) {
    return { canVote: false, reason: 'User has already voted in this election' };
  }
  
  // Check geographic eligibility
  const userConstituency = user.address.constituency;
  const isEligibleConstituency = this.geographicCoverage.constituencies.includes(userConstituency);
  
  if (!isEligibleConstituency) {
    return { canVote: false, reason: 'User is not in an eligible constituency' };
  }
  
  return { canVote: true };
};

module.exports = mongoose.model('Election', electionSchema);
