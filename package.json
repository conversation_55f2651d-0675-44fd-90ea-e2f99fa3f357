{"name": "ai-voting-system", "version": "1.0.0", "description": "AI-Enabled Online Voting System with Biometric Verification", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm start", "server": "nodemon server.js", "build": "cd client && npm run build", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["voting", "ai", "biometric", "face-recognition", "election", "democracy"], "author": "AI Voting System Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "nodemailer": "^6.9.4", "twilio": "^4.15.0", "socket.io": "^4.7.2", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "crypto": "^1.0.1", "node-cron": "^3.0.2", "winston": "^3.10.0", "compression": "^1.7.4", "express-session": "^1.17.3", "connect-mongo": "^5.0.0", "passport": "^0.6.0", "passport-local": "^1.0.0", "axios": "^1.5.0", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "@types/jest": "^29.5.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}