const express = require('express');
const {
  getElectionResults,
  getRealTimeStats,
  getGeographicAnalysis,
  getVoterTurnoutAnalysis,
  getSecurityAnalysis
} = require('../controllers/analyticsController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All analytics routes require authentication
router.use(protect);

// Most analytics routes require admin or election officer role
router.use(authorize('admin', 'election_officer', 'super_admin', 'analyst', 'observer'));

// Get election results
router.get('/elections/:electionId/results', getElectionResults);

// Get real-time election statistics
router.get('/elections/:electionId/real-time', getRealTimeStats);

// Get geographic analysis
router.get('/elections/:electionId/geographic', getGeographicAnalysis);

// Get voter turnout analysis
router.get('/elections/:electionId/turnout', getVoterTurnoutAnalysis);

// Get security analysis (restricted to super admins)
router.get(
  '/elections/:electionId/security',
  authorize('admin', 'super_admin'), // More restrictive access
  getSecurityAnalysis
);

module.exports = router;
