const Election = require('../models/Election');
const Vote = require('../models/Vote');
const User = require('../models/User');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Get election results
const getElectionResults = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission to view results
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.permissions.includes('view')
  ) || req.user.role === 'admin';

  if (!hasPermission) {
    return next(new AppError('Not authorized to view election results', 403));
  }

  try {
    // Get overall results
    const overallResults = await Vote.getElectionResults(electionId);
    
    // Get geographic results
    const stateResults = await Vote.getGeographicResults(electionId, 'state');
    const districtResults = await Vote.getGeographicResults(electionId, 'district');
    const constituencyResults = await Vote.getGeographicResults(electionId, 'constituency');
    
    // Get voting patterns
    const votingPatterns = await Vote.getVotingPatterns(electionId);
    
    // Get total vote count
    const totalVotes = await Vote.countDocuments({ 
      electionId, 
      isValid: true 
    });
    
    // Get invalid votes
    const invalidVotes = await Vote.countDocuments({ 
      electionId, 
      isValid: false 
    });
    
    // Get suspicious votes
    const suspiciousVotes = await Vote.countDocuments({ 
      electionId, 
      'security.isSuspicious': true 
    });

    // Calculate voter turnout
    const eligibleVoters = await User.countDocuments({
      isEligible: true,
      $or: [
        { 'address.constituency': { $in: election.geographicCoverage.constituencies } },
        { 'address.state': { $in: election.geographicCoverage.states } }
      ]
    });

    const voterTurnout = eligibleVoters > 0 ? (totalVotes / eligibleVoters) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        election: {
          _id: election._id,
          title: election.title,
          type: election.type,
          status: election.status,
          startDate: election.startDate,
          endDate: election.endDate
        },
        summary: {
          totalVotes,
          invalidVotes,
          suspiciousVotes,
          eligibleVoters,
          voterTurnout: parseFloat(voterTurnout.toFixed(2))
        },
        results: {
          overall: overallResults,
          geographic: {
            byState: stateResults,
            byDistrict: districtResults,
            byConstituency: constituencyResults
          }
        },
        patterns: {
          votingByTime: votingPatterns
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get election results:', error);
    return next(new AppError('Failed to retrieve election results', 500));
  }
});

// Get real-time election statistics
const getRealTimeStats = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check if user has permission
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString()
  ) || req.user.role === 'admin';

  if (!hasPermission) {
    return next(new AppError('Not authorized to view election statistics', 403));
  }

  try {
    // Get current vote counts
    const totalVotes = await Vote.countDocuments({ 
      electionId, 
      isValid: true 
    });

    // Get votes in last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const votesLastHour = await Vote.countDocuments({
      electionId,
      isValid: true,
      castedAt: { $gte: oneHourAgo }
    });

    // Get candidate-wise current standings
    const candidateResults = await Vote.aggregate([
      { 
        $match: { 
          electionId: election._id, 
          isValid: true 
        } 
      },
      {
        $group: {
          _id: '$candidateId',
          candidateName: { $first: '$candidateName' },
          party: { $first: '$party' },
          votes: { $sum: 1 }
        }
      },
      { $sort: { votes: -1 } }
    ]);

    // Calculate percentages
    const candidatesWithPercentage = candidateResults.map(candidate => ({
      ...candidate,
      percentage: totalVotes > 0 ? ((candidate.votes / totalVotes) * 100).toFixed(2) : 0
    }));

    // Get geographic distribution
    const stateDistribution = await Vote.aggregate([
      { 
        $match: { 
          electionId: election._id, 
          isValid: true 
        } 
      },
      {
        $group: {
          _id: '$geographic.state',
          votes: { $sum: 1 }
        }
      },
      { $sort: { votes: -1 } }
    ]);

    // Get hourly voting pattern for today
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    
    const hourlyPattern = await Vote.aggregate([
      {
        $match: {
          electionId: election._id,
          isValid: true,
          castedAt: { $gte: todayStart }
        }
      },
      {
        $group: {
          _id: { $hour: '$castedAt' },
          votes: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Get verification statistics
    const verificationStats = await Vote.aggregate([
      { 
        $match: { 
          electionId: election._id, 
          isValid: true 
        } 
      },
      {
        $group: {
          _id: null,
          totalVotes: { $sum: 1 },
          faceVerified: {
            $sum: { $cond: ['$verification.faceVerification.status', 1, 0] }
          },
          voiceVerified: {
            $sum: { $cond: ['$verification.voiceVerification.status', 1, 0] }
          },
          livenessChecked: {
            $sum: { $cond: ['$verification.livenessCheck.status', 1, 0] }
          },
          multipleVoicesDetected: {
            $sum: { $cond: ['$verification.voiceVerification.multipleVoicesDetected', 1, 0] }
          }
        }
      }
    ]);

    const verificationData = verificationStats[0] || {
      totalVotes: 0,
      faceVerified: 0,
      voiceVerified: 0,
      livenessChecked: 0,
      multipleVoicesDetected: 0
    };

    res.status(200).json({
      success: true,
      data: {
        election: {
          _id: election._id,
          title: election.title,
          status: election.status
        },
        realTimeStats: {
          totalVotes,
          votesLastHour,
          lastUpdated: new Date()
        },
        candidates: candidatesWithPercentage,
        geographic: {
          byState: stateDistribution
        },
        patterns: {
          hourlyToday: hourlyPattern
        },
        verification: {
          faceVerificationRate: verificationData.totalVotes > 0 ? 
            ((verificationData.faceVerified / verificationData.totalVotes) * 100).toFixed(2) : 0,
          voiceVerificationRate: verificationData.totalVotes > 0 ? 
            ((verificationData.voiceVerified / verificationData.totalVotes) * 100).toFixed(2) : 0,
          livenessCheckRate: verificationData.totalVotes > 0 ? 
            ((verificationData.livenessChecked / verificationData.totalVotes) * 100).toFixed(2) : 0,
          multipleVoicesRate: verificationData.totalVotes > 0 ? 
            ((verificationData.multipleVoicesDetected / verificationData.totalVotes) * 100).toFixed(2) : 0
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get real-time stats:', error);
    return next(new AppError('Failed to retrieve real-time statistics', 500));
  }
});

// Get geographic analysis
const getGeographicAnalysis = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;
  const { level = 'state' } = req.query; // state, district, constituency, city, village

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check permissions
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString()
  ) || req.user.role === 'admin';

  if (!hasPermission) {
    return next(new AppError('Not authorized to view geographic analysis', 403));
  }

  try {
    const geographicData = await Vote.getGeographicResults(electionId, level);
    
    // Get voter turnout by geographic level
    const turnoutData = await Vote.aggregate([
      { 
        $match: { 
          electionId: election._id, 
          isValid: true 
        } 
      },
      {
        $group: {
          _id: `$geographic.${level}`,
          totalVotes: { $sum: 1 },
          uniqueVoters: { $addToSet: '$voterHash' }
        }
      },
      {
        $addFields: {
          uniqueVoterCount: { $size: '$uniqueVoters' }
        }
      },
      {
        $project: {
          uniqueVoters: 0 // Remove the array to reduce response size
        }
      },
      { $sort: { totalVotes: -1 } }
    ]);

    // Get demographic analysis if available
    const demographicAnalysis = await Vote.aggregate([
      { 
        $match: { 
          electionId: election._id, 
          isValid: true 
        } 
      },
      {
        $group: {
          _id: {
            location: `$geographic.${level}`,
            hour: { $hour: '$castedAt' }
          },
          votes: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.location',
          votingPattern: {
            $push: {
              hour: '$_id.hour',
              votes: '$votes'
            }
          },
          totalVotes: { $sum: '$votes' }
        }
      },
      { $sort: { totalVotes: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        election: {
          _id: election._id,
          title: election.title
        },
        level: level,
        results: geographicData,
        turnout: turnoutData,
        patterns: demographicAnalysis
      }
    });
  } catch (error) {
    logger.error('Failed to get geographic analysis:', error);
    return next(new AppError('Failed to retrieve geographic analysis', 500));
  }
});

// Get voter turnout analysis
const getVoterTurnoutAnalysis = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check permissions
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString()
  ) || req.user.role === 'admin';

  if (!hasPermission) {
    return next(new AppError('Not authorized to view turnout analysis', 403));
  }

  try {
    // Get total eligible voters
    const eligibleVoters = await User.countDocuments({
      isEligible: true,
      $or: [
        { 'address.constituency': { $in: election.geographicCoverage.constituencies || [] } },
        { 'address.state': { $in: election.geographicCoverage.states || [] } }
      ]
    });

    // Get actual votes
    const actualVotes = await Vote.countDocuments({
      electionId: election._id,
      isValid: true
    });

    // Get turnout by time periods
    const hourlyTurnout = await Vote.aggregate([
      {
        $match: {
          electionId: election._id,
          isValid: true
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$castedAt' } },
            hour: { $hour: '$castedAt' }
          },
          votes: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          hourlyData: {
            $push: {
              hour: '$_id.hour',
              votes: '$votes'
            }
          },
          dailyTotal: { $sum: '$votes' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Get turnout by geographic regions
    const geographicTurnout = await Vote.aggregate([
      {
        $match: {
          electionId: election._id,
          isValid: true
        }
      },
      {
        $group: {
          _id: {
            state: '$geographic.state',
            district: '$geographic.district'
          },
          votes: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.state',
          districts: {
            $push: {
              district: '$_id.district',
              votes: '$votes'
            }
          },
          stateTotal: { $sum: '$votes' }
        }
      },
      { $sort: { stateTotal: -1 } }
    ]);

    // Calculate overall turnout percentage
    const turnoutPercentage = eligibleVoters > 0 ? (actualVotes / eligibleVoters) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        election: {
          _id: election._id,
          title: election.title,
          startDate: election.startDate,
          endDate: election.endDate
        },
        summary: {
          eligibleVoters,
          actualVotes,
          turnoutPercentage: parseFloat(turnoutPercentage.toFixed(2))
        },
        patterns: {
          byTime: hourlyTurnout,
          byGeography: geographicTurnout
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get voter turnout analysis:', error);
    return next(new AppError('Failed to retrieve voter turnout analysis', 500));
  }
});

// Get security analysis
const getSecurityAnalysis = catchAsync(async (req, res, next) => {
  const { electionId } = req.params;

  const election = await Election.findById(electionId);
  if (!election) {
    return next(new AppError('Election not found', 404));
  }

  // Check permissions (only super admins can view security analysis)
  const hasPermission = election.administrators.some(
    admin => admin.userId.toString() === req.user._id.toString() &&
    admin.role === 'super_admin'
  ) || req.user.role === 'admin';

  if (!hasPermission) {
    return next(new AppError('Not authorized to view security analysis', 403));
  }

  try {
    // Get suspicious votes
    const suspiciousVotes = await Vote.find({
      electionId: election._id,
      'security.isSuspicious': true
    }).select('security verification session.ipAddress castedAt');

    // Get verification failure rates
    const verificationStats = await Vote.aggregate([
      { $match: { electionId: election._id } },
      {
        $group: {
          _id: null,
          totalVotes: { $sum: 1 },
          faceVerificationFailed: {
            $sum: { $cond: [{ $eq: ['$verification.faceVerification.status', false] }, 1, 0] }
          },
          voiceVerificationFailed: {
            $sum: { $cond: [{ $eq: ['$verification.voiceVerification.status', false] }, 1, 0] }
          },
          multipleVoicesDetected: {
            $sum: { $cond: ['$verification.voiceVerification.multipleVoicesDetected', 1, 0] }
          },
          livenessCheckFailed: {
            $sum: { $cond: [{ $eq: ['$verification.livenessCheck.status', false] }, 1, 0] }
          },
          highRiskVotes: {
            $sum: { $cond: [{ $gte: ['$security.riskScore', 50] }, 1, 0] }
          }
        }
      }
    ]);

    // Get IP address analysis
    const ipAnalysis = await Vote.aggregate([
      { $match: { electionId: election._id } },
      {
        $group: {
          _id: '$session.ipAddress',
          voteCount: { $sum: 1 },
          suspiciousCount: {
            $sum: { $cond: ['$security.isSuspicious', 1, 0] }
          }
        }
      },
      { $match: { voteCount: { $gt: 1 } } }, // IPs with multiple votes
      { $sort: { voteCount: -1 } },
      { $limit: 20 }
    ]);

    const stats = verificationStats[0] || {};

    res.status(200).json({
      success: true,
      data: {
        election: {
          _id: election._id,
          title: election.title
        },
        summary: {
          totalVotes: stats.totalVotes || 0,
          suspiciousVotes: suspiciousVotes.length,
          highRiskVotes: stats.highRiskVotes || 0
        },
        verificationFailures: {
          faceVerification: stats.faceVerificationFailed || 0,
          voiceVerification: stats.voiceVerificationFailed || 0,
          multipleVoices: stats.multipleVoicesDetected || 0,
          livenessCheck: stats.livenessCheckFailed || 0
        },
        suspiciousActivity: {
          flaggedVotes: suspiciousVotes.map(vote => ({
            voteId: vote._id,
            riskScore: vote.security.riskScore,
            reasons: vote.security.suspiciousReasons,
            ipAddress: vote.session.ipAddress,
            timestamp: vote.castedAt
          })),
          ipAnalysis: ipAnalysis
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get security analysis:', error);
    return next(new AppError('Failed to retrieve security analysis', 500));
  }
});

module.exports = {
  getElectionResults,
  getRealTimeStats,
  getGeographicAnalysis,
  getVoterTurnoutAnalysis,
  getSecurityAnalysis
};
