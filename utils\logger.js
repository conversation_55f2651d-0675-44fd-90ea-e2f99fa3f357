const winston = require('winston');
const path = require('path');

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which logs to print based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: logFormat
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(__dirname, '../logs/error.log'),
    level: 'error',
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(__dirname, '../logs/combined.log'),
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  })
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileLogFormat,
  transports,
  exitOnError: false
});

// Create a stream object for Morgan HTTP logger
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  }
};

// Add security logging methods
logger.security = {
  loginAttempt: (email, ip, success, reason = null) => {
    logger.info(`Login attempt: ${email} from ${ip} - ${success ? 'SUCCESS' : 'FAILED'}${reason ? ` (${reason})` : ''}`);
  },
  
  suspiciousActivity: (userId, activity, details, ip) => {
    logger.warn(`Suspicious activity detected: User ${userId} - ${activity} from ${ip}. Details: ${JSON.stringify(details)}`);
  },
  
  dataAccess: (userId, resource, action, ip) => {
    logger.info(`Data access: User ${userId} performed ${action} on ${resource} from ${ip}`);
  },
  
  systemEvent: (event, details, severity = 'info') => {
    logger[severity](`System event: ${event} - ${JSON.stringify(details)}`);
  },
  
  auditEvent: (userId, action, resource, result, ip) => {
    logger.info(`Audit: User ${userId} ${action} ${resource} - ${result} from ${ip}`);
  }
};

// Add performance logging
logger.performance = {
  apiCall: (method, endpoint, duration, statusCode, userId = null) => {
    const message = `API: ${method} ${endpoint} - ${duration}ms - ${statusCode}${userId ? ` (User: ${userId})` : ''}`;
    
    if (duration > 5000) { // Log slow requests as warnings
      logger.warn(`SLOW ${message}`);
    } else {
      logger.http(message);
    }
  },
  
  dbQuery: (operation, collection, duration, recordCount = null) => {
    const message = `DB: ${operation} on ${collection} - ${duration}ms${recordCount ? ` (${recordCount} records)` : ''}`;
    
    if (duration > 1000) { // Log slow queries as warnings
      logger.warn(`SLOW ${message}`);
    } else {
      logger.debug(message);
    }
  }
};

// Add election-specific logging
logger.election = {
  created: (electionId, title, createdBy) => {
    logger.info(`Election created: ${title} (ID: ${electionId}) by user ${createdBy}`);
  },
  
  started: (electionId, title) => {
    logger.info(`Election started: ${title} (ID: ${electionId})`);
  },
  
  ended: (electionId, title, totalVotes) => {
    logger.info(`Election ended: ${title} (ID: ${electionId}) with ${totalVotes} total votes`);
  },
  
  voteCast: (electionId, candidateId, sessionId, verificationStatus) => {
    logger.info(`Vote cast: Election ${electionId}, Candidate ${candidateId}, Session ${sessionId}, Verification: ${JSON.stringify(verificationStatus)}`);
  },
  
  suspiciousVote: (electionId, sessionId, reasons, riskScore) => {
    logger.warn(`Suspicious vote detected: Election ${electionId}, Session ${sessionId}, Risk Score: ${riskScore}, Reasons: ${reasons.join(', ')}`);
  },
  
  candidateAdded: (electionId, candidateName, addedBy) => {
    logger.info(`Candidate added: ${candidateName} to election ${electionId} by user ${addedBy}`);
  },
  
  resultsPublished: (electionId, publishedBy) => {
    logger.info(`Results published: Election ${electionId} by user ${publishedBy}`);
  }
};

// Add error handling for uncaught exceptions
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.File({
    filename: path.join(__dirname, '../logs/exceptions.log'),
    handleExceptions: true,
    handleRejections: true,
    format: fileLogFormat
  }));
}

// Export the logger
module.exports = logger;
