# AI-Enabled Online Voting System

A secure, transparent, and AI-powered online voting platform with biometric verification, real-time monitoring, and comprehensive audit trails.

## 🚀 Features

### Core Voting Features
- **Secure Online Voting**: End-to-end encrypted voting process
- **Real-time Results**: Live election monitoring and results
- **Multi-level Elections**: Support for national, state, and local elections
- **Candidate Management**: Comprehensive candidate profiles and management

### AI-Powered Security
- **Face Recognition**: AI-powered face verification using OpenCV and TensorFlow
- **Voice Detection**: Multi-speaker detection and voice verification
- **Document Verification**: Automated Aadhaar and Voter ID verification
- **Liveness Detection**: Anti-spoofing measures for biometric verification
- **Suspicious Activity Detection**: AI-powered fraud detection

### Security & Compliance
- **Multi-factor Authentication**: Email, SMS, and biometric verification
- **Comprehensive Audit Logs**: Complete trail of all system activities
- **Role-based Access Control**: Admin, election officer, and voter roles
- **IP Whitelisting**: Restricted admin access
- **Rate Limiting**: Protection against brute force attacks
- **Session Management**: Secure session handling

### Real-time Features
- **WebSocket Integration**: Real-time updates and notifications
- **Live Monitoring**: Real-time election monitoring dashboard
- **Instant Notifications**: Immediate alerts for security events
- **Live Results**: Real-time vote counting and results

### Analytics & Reporting
- **Geographic Analysis**: Voting patterns by location
- **Turnout Analysis**: Voter participation statistics
- **Security Reports**: Comprehensive security incident reporting
- **Performance Metrics**: System performance monitoring

## 🛠️ Technology Stack

### Backend
- **Node.js** with Express.js framework
- **MongoDB** with Mongoose ODM
- **Socket.IO** for real-time communication
- **JWT** for authentication
- **Winston** for logging
- **Multer** for file uploads
- **Sharp** for image processing

### AI/ML Components
- **OpenCV** for computer vision
- **TensorFlow.js** for machine learning
- **FaceNet** for face recognition
- **Custom voice detection algorithms**

### Frontend
- **React** with TypeScript
- **Material-UI** for components
- **React Router** for navigation
- **React Query** for data fetching
- **Socket.IO Client** for real-time updates
- **Recharts** for data visualization

### Security
- **Helmet.js** for security headers
- **bcryptjs** for password hashing
- **express-rate-limit** for rate limiting
- **CORS** configuration
- **express-validator** for input validation

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (v5 or higher)
- Python (v3.8 or higher) for AI components
- OpenCV libraries
- FFmpeg for audio processing

## 🚀 Installation

### 1. Clone the repository
```bash
git clone https://github.com/your-username/ai-voting-system.git
cd ai-voting-system
```

### 2. Install backend dependencies
```bash
npm install
```

### 3. Install frontend dependencies
```bash
cd client
npm install
cd ..
```

### 4. Install Python dependencies (for AI components)
```bash
pip install opencv-python tensorflow librosa numpy
```

### 5. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 6. Set up MongoDB
- Install MongoDB locally or use MongoDB Atlas
- Update MONGODB_URI in .env file

### 7. Create required directories
```bash
mkdir uploads logs
mkdir uploads/temp
mkdir models
```

## 🔧 Configuration

### Environment Variables
Key environment variables to configure:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/voting-system

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_PHONE_NUMBER=your-phone-number

# AI Configuration
FACE_RECOGNITION_THRESHOLD=0.6
VOICE_DETECTION_THRESHOLD=0.7
```

### AI Models Setup
1. Download pre-trained models (optional):
   - FaceNet model for face recognition
   - Haar cascades for face detection

2. Place models in the `models/` directory

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Start backend server
npm run dev

# Start frontend (in another terminal)
npm run client

# Or start both concurrently
npm run dev
```

### Production Mode
```bash
# Build frontend
npm run build

# Start production server
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/health

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/verify-email` - Verify email OTP
- `POST /api/auth/verify-phone` - Verify phone OTP

### Voting Endpoints
- `GET /api/voting/elections` - Get available elections
- `GET /api/voting/elections/:id` - Get election details
- `POST /api/voting/elections/:id/start-session` - Start voting session
- `POST /api/voting/elections/:id/verify-face` - Face verification
- `POST /api/voting/elections/:id/verify-voice` - Voice verification
- `POST /api/voting/elections/:id/cast-vote` - Cast vote

### Admin Endpoints
- `GET /api/admin/dashboard` - Admin dashboard stats
- `GET /api/admin/users` - Manage users
- `POST /api/admin/elections` - Create election
- `GET /api/admin/elections` - Manage elections

### Upload Endpoints
- `POST /api/upload/documents` - Upload verification documents
- `POST /api/upload/verify-face` - Face verification against documents

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Multi-factor authentication (MFA)
- Session management with MongoDB store

### Biometric Verification
- Face recognition with liveness detection
- Voice verification with multi-speaker detection
- Document verification (Aadhaar, Voter ID)
- Anti-spoofing measures

### System Security
- Rate limiting on all endpoints
- IP whitelisting for admin access
- Comprehensive audit logging
- Input validation and sanitization
- CORS configuration
- Security headers with Helmet.js

### Data Protection
- Password hashing with bcrypt
- Encrypted data transmission
- Secure file upload handling
- Anonymous vote storage
- GDPR compliance features

## 📊 Monitoring & Analytics

### Real-time Monitoring
- Live election statistics
- Real-time vote counting
- System health monitoring
- Security incident alerts

### Analytics Dashboard
- Voter turnout analysis
- Geographic voting patterns
- Security incident reports
- Performance metrics

### Audit Trails
- Complete user activity logs
- Election event tracking
- Security incident logging
- System access monitoring

## 🧪 Testing

### Run Tests
```bash
# Backend tests
npm test

# Frontend tests
cd client && npm test

# Coverage report
npm run test:coverage
```

### Test Categories
- Unit tests for core functions
- Integration tests for API endpoints
- Security tests for authentication
- Performance tests for load handling

## 🚀 Deployment

### Production Checklist
- [ ] Set NODE_ENV=production
- [ ] Configure production database
- [ ] Set up SSL certificates
- [ ] Configure reverse proxy (Nginx)
- [ ] Set up monitoring (PM2, New Relic)
- [ ] Configure backup strategy
- [ ] Set up CI/CD pipeline

### Docker Deployment
```bash
# Build and run with Docker
docker-compose up --build
```

### Cloud Deployment
- AWS EC2 with MongoDB Atlas
- Google Cloud Platform
- Microsoft Azure
- Heroku (for development)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Wiki](https://github.com/your-username/ai-voting-system/wiki)

## 🙏 Acknowledgments

- OpenCV community for computer vision libraries
- TensorFlow team for machine learning framework
- React and Node.js communities
- All contributors and testers

---

**⚠️ Important Security Notice**: This system is designed for educational and demonstration purposes. For production use in actual elections, additional security audits, compliance certifications, and legal approvals are required.
